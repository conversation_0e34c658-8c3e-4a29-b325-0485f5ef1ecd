package com.daddylab.supplier.item.application.shipinghao.dto.wechatProduct;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List; /** 商品SKU信息 */
@Data
public class Sku {
  /** SKU ID */
  @JsonProperty("sku_id")
  private String skuId;

  /** 外部SKU ID（第三方SKU ID） */
  @JsonProperty("out_sku_id")
  private String outSkuId;

  /** SKU缩略图 */
  @JsonProperty("thumb_img")
  private String thumbImg;

  /** SKU售价，单位：分 */
  @JsonProperty("sale_price")
  private Integer salePrice;

  /** SKU库存数量 */
  @JsonProperty("stock_num")
  private Integer stockNum;

  /** SKU编码 */
  @JsonProperty("sku_code")
  private String skuCode;

  /** SKU属性列表（规格信息） */
  @JsonProperty("sku_attrs")
  private List<Attribute> skuAttrs;

  /** SKU状态：1-待审核，2-审核中，3-审核失败，4-审核成功，5-上架，6-下架 */
  private Integer status;

  /** SKU发货信息 */
  @JsonProperty("sku_deliver_info")
  private SkuDeliverInfo skuDeliverInfo;

  /** 条形码 */
  @JsonProperty("bar_code")
  private String barCode;
}
