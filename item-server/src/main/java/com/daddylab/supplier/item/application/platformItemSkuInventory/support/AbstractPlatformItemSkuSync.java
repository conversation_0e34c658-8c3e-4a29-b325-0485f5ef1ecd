package com.daddylab.supplier.item.application.platformItemSkuInventory.support;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.daddylab.supplier.item.application.platformItem.PlatformItemStockSyncBizService;
import com.daddylab.supplier.item.application.platformItem.config.PlatformItemSyncConfig;
import com.daddylab.supplier.item.application.platformItemSkuInventory.PlatformItemSkuInventoryBizService;
import com.daddylab.supplier.item.application.platformItemSkuInventory.PlatformItemSkuSyncService;
import com.daddylab.supplier.item.application.platformItemSkuInventory.converter.PlatformItemSkuInventoryConverter;
import com.daddylab.supplier.item.application.platformItemSkuInventory.domain.dto.PlatformItemChangeEvent;
import com.daddylab.supplier.item.application.platformItemSkuInventory.domain.params.PlatformSkuInventorySyncInfo;
import com.daddylab.supplier.item.application.platformItemSkuInventory.domain.params.PlatformSkuSyncInfo;
import com.daddylab.supplier.item.domain.platformItem.service.PlatformItemSyncService;
import com.daddylab.supplier.item.infrastructure.config.batch.reader.mybatisplus.MybatisPlusPagingItemReader;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.PlatformItem;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.PlatformItemSku;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.StockSyncRecord;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.StockSyncDirection;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.StockSyncStatus;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IPlatformItemService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IPlatformItemSkuService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IShopAuthorizationService;
import groovy.lang.Lazy;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.core.*;
import org.springframework.batch.core.configuration.annotation.JobBuilderFactory;
import org.springframework.batch.core.configuration.annotation.StepBuilderFactory;
import org.springframework.batch.core.job.builder.SimpleJobBuilder;
import org.springframework.batch.core.launch.JobLauncher;
import org.springframework.batch.core.listener.ChunkListenerSupport;
import org.springframework.batch.core.listener.StepExecutionListenerSupport;
import org.springframework.batch.core.scope.context.ChunkContext;
import org.springframework.batch.core.step.tasklet.CallableTaskletAdapter;
import org.springframework.batch.core.step.tasklet.Tasklet;
import org.springframework.batch.item.ItemProcessor;
import org.springframework.batch.item.ItemReader;
import org.springframework.batch.item.ItemWriter;
import org.springframework.batch.repeat.RepeatStatus;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.dao.DataAccessException;
import org.springframework.retry.backoff.FixedBackOffPolicy;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.io.IOException;
import java.time.Duration;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @class AbstractItemSkuSync.java
 * @description
 * @date 2024-03-14 09:50
 */
@Slf4j
public abstract class AbstractPlatformItemSkuSync<I, O> implements PlatformItemSkuSyncService {

    static final String PLATFORM_ITEM_SKU_SYNC_JOB = "platformItemSkuSyncJob";

    @Autowired
    protected JobLauncher jobLauncher;
    @Autowired
    protected JobBuilderFactory jobBuilderFactory;
    @Autowired
    StepBuilderFactory stepBuilderFactory;
    @Autowired
    @Qualifier("syncExecutor")
    ThreadPoolTaskExecutor threadPoolTaskExecutor;
    @Autowired
    @Lazy
    PlatformItemSkuInventoryBizService platformItemSkuInventoryBizService;
    @Autowired
    IPlatformItemService platformItemService;
    @Autowired
    IPlatformItemSkuService platformItemSkuService;
    @Autowired
    PlatformItemSyncService platformItemSyncService;
    @Autowired
    PlatformItemSyncConfig platformItemSyncConfig;
    @Autowired
    PlatformItemStockSyncBizService platformItemStockSyncBizService;

    @Autowired
    protected IShopAuthorizationService shopAuthorizationService;

    /**
     * 执行job
     *
     * @date 2024/3/14 10:00
     * <AUTHOR>
     */
    BatchStatus run(Step... steps) {
        try {
            SimpleJobBuilder simpleJobBuilder = jobBuilderFactory.get(PLATFORM_ITEM_SKU_SYNC_JOB)
                                                                 .start(steps[0]);
            for (int i = 1; i < steps.length; i++) {
                final Step step = steps[i];
                simpleJobBuilder.next(step);
            }
            Job job = simpleJobBuilder.build();
            JobParametersBuilder parametersBuilder = new JobParametersBuilder();
            parametersBuilder.addDate("date", new Date());
            JobExecution run = jobLauncher.run(job, parametersBuilder.toJobParameters());
            if (run.getStatus().equals(BatchStatus.COMPLETED)) {
                log.info("[任务执行] 成功");
            }
            return run.getStatus();
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }


    public Step buildStep(ItemReader<I> itemReader) {
        return buildStep(itemReader, 100, "");
    }


    /**
     * 构建全量查询step
     *
     * @param itemReader
     * @param chunkSize
     * @return org.springframework.batch.core.Step
     * @date 2024/3/14 11:57
     * <AUTHOR>
     */
    public Step buildStep(ItemReader<I> itemReader, int chunkSize) {
        return buildStep(itemReader, chunkSize, "");
    }

    /**
     * 构建全量查询step
     *
     * @return org.springframework.batch.core.Step
     * @date 2024/3/14 11:57
     * <AUTHOR>
     */
    public Step buildStep(ItemReader<I> itemReader, int chunkSize, String stepName) {
        final String name = defaultType().getDesc() + stepName;
        return stepBuilderFactory.get(name)
                                 .<I, O>chunk(chunkSize)
                                 .reader(itemReader)
                                 .processor(getItemProcess())
                                 .writer(getItemWriter())
                                 .faultTolerant()
                                 .retry(DataAccessException.class)
                                 .retry(IOException.class)
                                 .backOffPolicy(new FixedBackOffPolicy())
                                 .retryLimit(3)
                                 .listener(getProgressListener(name))
                                 .listener(getStepExecutionListener(name))
                                 .taskExecutor(threadPoolTaskExecutor)
                                 .build();
    }

    protected StepExecutionListener getStepExecutionListener(String name) {
        return new StepExecutionListenerSupport() {
            LocalDateTime startTime;
            LocalDateTime endTime;

            @Override
            public void beforeStep(StepExecution stepExecution) {
                startTime = LocalDateTime.now();
                log.info("[平台商品同步][{}]{}开始", name, startTime);
            }

            @Override
            public ExitStatus afterStep(StepExecution stepExecution) {
                endTime = LocalDateTime.now();
                log.info("[平台商品同步][{}]{}开始 {}结束 耗时{}s",
                        name,
                        startTime,
                        endTime,
                        Duration.between(startTime, endTime).getSeconds());
                return stepExecution.getExitStatus();
            }
        };
    }

    @NonNull
    protected ChunkListener getProgressListener(String name) {
        return new ChunkListenerSupport() {
            private final AtomicInteger chunk = new AtomicInteger();

            @Override
            public void afterChunk(ChunkContext context) {
                final int n = chunk.incrementAndGet();
                log.info("[平台商品同步][{}]第{}批数据处理完成", name, n);
            }
        };
    }

    /**
     * 构建简单tasklet step
     *
     * @param tasklet Tasklet
     * @return org.springframework.batch.core.Step
     * @date 2024/3/14 16:59
     * <AUTHOR>
     */
    public Step getTaskletStep(Tasklet tasklet) {
        return stepBuilderFactory.get(String.format("[%s]-StatisticsStep", defaultType().getDesc()))
                                 .tasklet(tasklet)
                                 .build();
    }

    public Step getStatisticsStep() {
        return getTaskletStep(getStatisticsTasklet());
    }


    public Tasklet getStatisticsTasklet() {
        CallableTaskletAdapter callableTaskletAdapter = new CallableTaskletAdapter();
        callableTaskletAdapter.setCallable(() -> {
            log.info("[任务结束][{}]", defaultType().getDesc());
            return RepeatStatus.FINISHED;
        });
        return callableTaskletAdapter;
    }

    /**
     * 构建reader
     *
     * @param handler  Function<IPage<IPage<T>>
     * @param pageSize int
     * @return org.springframework.batch.item.ItemReader<T>
     * @date 2024/3/14 11:54
     * <AUTHOR>
     */
    public <T> ItemReader<T> getItemReader(Function<IPage<T>, IPage<T>> handler, int pageSize) {
        MybatisPlusPagingItemReader<T> mybatisPlusPagingItemReader = new MybatisPlusPagingItemReader<>();
        mybatisPlusPagingItemReader.setPageSize(pageSize);
        mybatisPlusPagingItemReader.setFunction(handler);
        return mybatisPlusPagingItemReader;
    }

    /**
     * 类型转换处理
     *
     * @return org.springframework.batch.item.ItemProcessor<I, com.daddylab.supplier.item.application.platformItemSkuInventory.domain.params.PlatformItemSkuInventoryParams>
     * @date 2024/3/14 13:37
     * <AUTHOR>
     */
    public abstract ItemProcessor<I, O> getItemProcess();

    /**
     * 默认数据保存方式
     *
     * @return org.springframework.batch.item.ItemWriter<com.daddylab.supplier.item.application.platformItemSkuInventory.domain.params.PlatformItemSkuInventoryParams>
     * @date 2024/3/14 11:37
     * <AUTHOR>
     */
    public abstract ItemWriter<O> getItemWriter();

    /**
     * 保存数据到
     *
     * @param skuSyncInfos List<PlatformItemSkuInventoryParams>
     * @date 2024/3/14 14:02
     * <AUTHOR>
     */
    void saveSkuSyncInfo(List<PlatformSkuSyncInfo> skuSyncInfos) {
        if (!CollUtil.isEmpty(skuSyncInfos)) {
            savePlatformItemSkus(skuSyncInfos);
            //saveInventories(skuSyncInfos);
        }
    }

    /**
     * 持久化平台商品数据
     *
     * @param skuSyncInfos 信息同步模型
     */
    protected void savePlatformItemSkus(List<PlatformSkuSyncInfo> skuSyncInfos) {
        final List<String> outerItemIds = skuSyncInfos.stream()
                                                      .map(PlatformSkuSyncInfo::getOuterItemId)
                                                      .distinct().collect(Collectors.toList());
        final List<PlatformItem> platformItems = platformItemService.lambdaQuery()
                                                                    .eq(PlatformItem::getPlatform, defaultType())
                                                                    .in(PlatformItem::getOuterItemId, outerItemIds)
                                                                    .list();
        final List<PlatformItemSku> platformItemSkuList = platformItemSkuService.lambdaQuery()
                                                                                .eq(PlatformItemSku::getPlatform,
                                                                                        defaultType())
                                                                                .in(PlatformItemSku::getOuterItemId,
                                                                                        outerItemIds)
                                                                                .list();
        final Map<String, PlatformItem> platformItemsMap = platformItems.stream()
                                                                        .collect(Collectors.toMap(PlatformItem::getOuterItemId,
                                                                                Function.identity(), (a, b) -> a));
        final Map<String, PlatformItemSku> platformItemSkusMap = platformItemSkuList.stream()
                                                                                    .collect(Collectors.toMap(
                                                                                            PlatformItemSku::getOuterSkuId,
                                                                                            Function.identity(),
                                                                                            (a, b) -> a));


        for (PlatformSkuSyncInfo skuSyncInfo : skuSyncInfos) {
            final PlatformItem platformItem = PlatformItemSkuInventoryConverter.INSTANCE.paramToPlatformItem(
                    skuSyncInfo);
            final PlatformItem platformItemLocal = platformItemsMap.get(skuSyncInfo.getOuterItemId());
            if (platformItemLocal != null) {
                platformItem.setId(platformItemLocal.getId());
            }
            final PlatformItemSku platformItemSku = PlatformItemSkuInventoryConverter.INSTANCE.paramToPlatformItemSku(
                    skuSyncInfo);
            final PlatformItemSku platformItemSkuLocal = platformItemSkusMap.get(platformItemSku.getOuterSkuId());
            if (platformItemSkuLocal != null) {
                platformItemSku.setId(platformItemSkuLocal.getId());
                if (skuSyncInfo.getStock() != null && !platformItemSkuLocal.getStock()
                                                                           .equals(skuSyncInfo.getStock().intValue())) {
                    stockRecord(platformItemSku, platformItemSkuLocal);
                }
                if (!Objects.equals(platformItemSkuLocal.getOuterSkuCode(), platformItemSku.getOuterSkuCode())) {
                    platformItemSku.setOuterSkuCodeModify(true);
                }
            }
            platformItemSyncService.matchAndSave(platformItem, platformItemSku);
            platformItemsMap.put(platformItem.getOuterItemId(), platformItem);
        }

    }

    private void stockRecord(PlatformItemSku platformItemSku, PlatformItemSku platformItemSkuLocal) {
        final StockSyncRecord stockSyncRecord = new StockSyncRecord();
        final long currentSeconds = DateUtil.currentSeconds();
        stockSyncRecord.setCreatedAt(currentSeconds);
        stockSyncRecord.setPlatform(platformItemSkuLocal.getPlatform());
        stockSyncRecord.setSyncDirection(StockSyncDirection.DOWNLOAD);
        stockSyncRecord.setSyncStatus(StockSyncStatus.SUCCESS);
        stockSyncRecord.setMsg("[平台商品同步][更新库存]");
        stockSyncRecord.setOuterItemId(platformItemSkuLocal.getOuterItemId());
        stockSyncRecord.setOuterSkuId(platformItemSkuLocal.getOuterSkuId());
        stockSyncRecord.setItemCode(platformItemSkuLocal.getOuterItemCode());
        stockSyncRecord.setSkuCode(platformItemSkuLocal.getOuterSkuCode());
        stockSyncRecord.setBeforeStock(platformItemSkuLocal.getStock());
        stockSyncRecord.setStock(platformItemSku.getStock());

        platformItemStockSyncBizService.putSyncLogAsync(stockSyncRecord);
    }

    @Override
    public void incrementSync(List<PlatformItemChangeEvent> events) {
        if (CollUtil.isEmpty(events)) {
            return;
        }
        for (PlatformItemChangeEvent event : events) {
            switch (event.getType()) {

                case ITEM_CHANGE:
                    handleItemChangeEvent(event);
                    break;
                case STOCK_CHANGE:
                    handleStockChangeEvent(event);
                    break;
            }
        }
    }

    /**
     * 处理平台商品信息变更事件
     *
     * @param event 事件
     */
    private void handleItemChangeEvent(PlatformItemChangeEvent event) {
        for (PlatformItemChangeEvent.ChangeItem item : event.getItems()) {
            List<PlatformSkuSyncInfo> params = getSkuSyncInfos(event.getShopNo(), item.getItemId());
            saveSkuSyncInfo(params);
        }
    }

    /**
     * 获取平台商品明细数据
     *
     * @param itemId 平台商品ID
     */
    protected abstract List<PlatformSkuSyncInfo> getSkuSyncInfos(String shopNo, String itemId);


    /**
     * 处理平台商品库存变更事件
     *
     * @param event 事件
     */
    private void handleStockChangeEvent(PlatformItemChangeEvent event) {
        final ArrayList<PlatformItemSku> updateModels = new ArrayList<>();
        for (PlatformItemChangeEvent.ChangeItem item : event.getItems()) {
            final List<PlatformSkuInventorySyncInfo> platformSkuInventorySyncInfos = getInventorySyncInfos(event.getShopNo(),
                    item.getItemId());
            final List<PlatformItemSku> platformItemSkuList = platformItemSkuService
                    .lambdaQuery()
                    .eq(PlatformItemSku::getPlatform, defaultType())
                    .eq(PlatformItemSku::getOuterItemId, item.getItemId())
                    .list();
            for (PlatformItemSku platformItemSku : platformItemSkuList) {
                for (PlatformSkuInventorySyncInfo platformSkuInventorySyncInfo : platformSkuInventorySyncInfos) {
                    if (platformSkuInventorySyncInfo.getOuterSkuId().equals(platformItemSku.getOuterSkuId())) {
                        final PlatformItemSku updateModel = new PlatformItemSku();
                        updateModel.setId(platformItemSku.getId());
                        updateModel.setStock(platformSkuInventorySyncInfo.getStock().intValue());
                        updateModel.setUpdatedAt(DateUtil.currentSeconds());
                        updateModels.add(updateModel);
                        break;
                    }
                }
            }
        }
        if (!updateModels.isEmpty()) {
            platformItemSkuService.updateBatchById(updateModels);
        }
    }

    /**
     * 各平台服务实现库存数据的查询
     *
     * @param outerItemId 平台商品ID
     */
    protected List<PlatformSkuInventorySyncInfo> getInventorySyncInfos(String shopNo, String outerItemId) {
        return getSkuSyncInfos(shopNo, outerItemId).stream()
                                                   .map(PlatformSkuSyncAssembler.INSTANCE::platformSkuSyncInfoToPlatformSkuInventorySyncInfo)
                                                   .collect(Collectors.toList());
    }

    @Override
    public void syncItem(String shopNo, String outerItemId) {
        final List<PlatformSkuSyncInfo> skuSyncInfos = getSkuSyncInfos(shopNo, outerItemId);
        saveSkuSyncInfo(skuSyncInfos);
    }

    @Override
    public void incrementSync(LocalDateTime startTime, LocalDateTime endTime) throws UnsupportedOperationException {
        throw new UnsupportedOperationException(String.format("%s 不支持按照时间增量同步", defaultType().getDesc()));
    }

}
