package com.daddylab.supplier.item.application.shipinghao.service.impl;

import com.alibaba.fastjson2.JSON;
import com.daddylab.supplier.item.application.shipinghao.config.WechatConfig;
import com.daddylab.supplier.item.application.shipinghao.dto.WechatAccessTokenDto;
import com.daddylab.supplier.item.application.shipinghao.dto.WechatProductDetailDto;
import com.daddylab.supplier.item.application.shipinghao.dto.WechatProductListDto;
import com.daddylab.supplier.item.application.shipinghao.service.WechatApiService;
import com.daddylab.supplier.item.infrastructure.utils.JsonUtil;
import com.daddylab.supplier.item.infrastructure.utils.RedisUtil;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 微信API服务实现
 *
 * <AUTHOR>
 * @since 2024-12-19
 */
@Slf4j
@Service
public class WechatApiServiceImpl implements WechatApiService {

  @Autowired private WechatConfig wechatConfig;

  private final OkHttpClient httpClient;

  public WechatApiServiceImpl() {
    this.httpClient =
        new OkHttpClient.Builder()
            .connectTimeout(10, TimeUnit.SECONDS)
            .readTimeout(30, TimeUnit.SECONDS)
            .writeTimeout(30, TimeUnit.SECONDS)
            .build();
  }

  @Override
  public WechatAccessTokenDto getAccessToken(String type) {
    try {
      // 先从Redis缓存中获取
      String cacheKey = getAccessTokenCacheKey(type);
      String cachedToken = RedisUtil.get(cacheKey);
      if (cachedToken != null && !cachedToken.isEmpty()) {
        return JsonUtil.parse(cachedToken, WechatAccessTokenDto.class);
      }

      // 缓存中没有，则调用API获取
      WechatAccessTokenDto accessToken = fetchAccessTokenFromApi(type);

      // 如果获取成功，则缓存到Redis
      if (accessToken != null && accessToken.isSuccess()) {
        cacheAccessToken(type, accessToken);
      }

      return accessToken;

    } catch (Exception e) {
      log.error("获取访问令牌异常，类型: {}", type, e);
      return null;
    }
  }

  /** 使用next_key获取商品列表（正确的分页方式） */
  public WechatProductListDto getProductListWithNextKey(
      String type, String accessToken, Integer pageSize, String nextKey) {
    try {
      int actualPageSize = Math.min(pageSize != null ? pageSize : 10, 30);

      String url = wechatConfig.getProductListUrl() + "?access_token=" + accessToken;

      // 使用正确的微信API参数
      String requestBody = JSON.toJSONString(new ProductListRequest(actualPageSize, nextKey, 1));

      RequestBody body =
          RequestBody.create(MediaType.parse("application/json; charset=utf-8"), requestBody);

      Request request = new Request.Builder().url(url).post(body).build();

      try (Response response = httpClient.newCall(request).execute()) {
        if (!response.isSuccessful()) {
          log.error("获取商品列表失败，类型: {}, HTTP状态码: {}", type, response.code());
          return null;
        }

        assert response.body() != null;
        String responseBody = response.body().string();

        WechatProductListDto apiResponse = JsonUtil.parse(responseBody, WechatProductListDto.class);

        assert apiResponse != null;
        if (apiResponse.isSuccess()) {
          return apiResponse;
        } else {
          log.error(
              "获取商品列表失败，类型: {}, 错误码: {}, 错误信息: {}",
              type,
              apiResponse.getErrcode(),
              apiResponse.getErrmsg());
          return null;
        }
      }
    } catch (IOException e) {
      log.error("获取商品列表异常，类型: {}", type, e);
      return null;
    }
  }

  @Override
  public WechatProductDetailDto getProductDetail(
      String type, String accessToken, String productId) {
    try {
      String url = wechatConfig.getProductDetailUrl() + "?access_token=" + accessToken;

      // 构建请求参数
      String requestBody = JSON.toJSONString(new ProductDetailRequest(productId));

      RequestBody body =
          RequestBody.create(MediaType.parse("application/json; charset=utf-8"), requestBody);

      Request request = new Request.Builder().url(url).post(body).build();

      try (Response response = httpClient.newCall(request).execute()) {
        if (!response.isSuccessful()) {
          log.error("获取商品详情失败，类型: {}, 商品ID: {}, HTTP状态码: {}", type, productId, response.code());
          return null;
        }

        assert response.body() != null;
        String responseBody = response.body().string();

        WechatProductDetailDto apiResponse =
            JsonUtil.parse(responseBody, WechatProductDetailDto.class);

        assert apiResponse != null;
        if (apiResponse.isSuccess()) {
          return apiResponse;
        } else {
          log.error(
              "获取商品详情失败，类型: {}, 商品ID: {}, 错误码: {}, 错误信息: {}",
              type,
              productId,
              apiResponse.getErrcode(),
              apiResponse.getErrmsg());
          return null;
        }
      }
    } catch (IOException e) {
      log.error("获取商品详情异常，类型: {}, 商品ID: {}", type, productId, e);
      return null;
    }
  }

  @Override
  public void clearCachedAccessToken(String type) {
    try {
      String cacheKey = getAccessTokenCacheKey(type);
      RedisUtil.del(cacheKey);
    } catch (Exception e) {
      log.error("清除访问令牌缓存异常，类型: {}", type, e);
    }
  }

  /** 从API获取访问令牌 */
  private WechatAccessTokenDto fetchAccessTokenFromApi(String type) {
    try {
      WechatConfig.WechatShopConfig shopConfig = wechatConfig.getShopConfig(type);
      if (shopConfig == null) {
        log.error("未找到类型为 {} 的微信小店配置", type);
        return null;
      }

      // 构建请求参数
      Map<String, Object> reqestMap = new HashMap<>(4);
      reqestMap.put("grant_type", "client_credential");
      reqestMap.put("appid", shopConfig.getAppId());
      reqestMap.put("secret", shopConfig.getAppSecret());
      String requestBody = JSON.toJSONString(reqestMap);

      RequestBody body =
          RequestBody.create(MediaType.parse("application/json; charset=utf-8"), requestBody);
      Request request =
          new Request.Builder().url(wechatConfig.getAccessTokenUrl()).post(body).build();
      try (Response response = httpClient.newCall(request).execute()) {
        if (!response.isSuccessful()) {
          log.error("调用API获取访问令牌失败，类型: {}, HTTP状态码: {}", type, response.code());
          return null;
        }

        assert response.body() != null;
        String responseBody = response.body().string();

        return JSON.parseObject(responseBody, WechatAccessTokenDto.class);
      }
    } catch (IOException e) {
      log.error("调用API获取访问令牌异常，类型: {}", type, e);
      return null;
    }
  }

  /** 缓存访问令牌到Redis */
  private void cacheAccessToken(String type, WechatAccessTokenDto accessToken) {
    try {
      String cacheKey = getAccessTokenCacheKey(type);
      // 设置过期时间为7000秒（比微信的7200秒稍短，确保安全）
      RedisUtil.set(
          cacheKey,
          JsonUtil.toJson(accessToken),
          accessToken.getExpiresIn() - 200,
          TimeUnit.SECONDS);
      log.info("访问令牌已缓存到Redis，类型: {}, 过期时间: 7000秒", type);
    } catch (Exception e) {
      log.error("缓存访问令牌到Redis异常，类型: {}", type, e);
    }
  }

  /** 获取访问令牌缓存key */
  private String getAccessTokenCacheKey(String type) {
    return "wechat:access_token:" + type;
  }

  /** 商品列表请求参数 */
  private static class ProductListRequest {
    private Integer page_size; // 改为page_size
    private String next_key; // 改为next_key
    private Integer data_type;

    // 兼容旧的构造函数（offset方式）
    //    public ProductListRequest(Integer offset, Integer limit, Integer data_type) {
    //      this.page_size = limit;
    //      this.next_key = null;  // offset方式不支持next_key
    //      this.data_type = data_type;
    //    }

    // 新的构造函数（正确的next_key方式）
    public ProductListRequest(Integer page_size, String next_key, Integer data_type) {
      this.page_size = page_size;
      this.next_key = next_key;
      this.data_type = data_type;
    }

    public Integer getPage_size() {
      return page_size;
    }

    public void setPage_size(Integer page_size) {
      this.page_size = page_size;
    }

    public String getNext_key() {
      return next_key;
    }

    public void setNext_key(String next_key) {
      this.next_key = next_key;
    }

    public Integer getData_type() {
      return data_type;
    }

    public void setData_type(Integer data_type) {
      this.data_type = data_type;
    }

    // 兼容性方法
    @Deprecated
    public Integer getOffset() {
      return null; // 微信API不支持offset
    }

    @Deprecated
    public void setOffset(Integer offset) {
      // 不做任何操作，微信API不支持offset
    }

    @Deprecated
    public Integer getLimit() {
      return page_size;
    }

    @Deprecated
    public void setLimit(Integer limit) {
      this.page_size = limit;
    }
  }

  /** 商品详情请求参数 */
  private static class ProductDetailRequest {
    private String product_id;

    public ProductDetailRequest(String product_id) {
      this.product_id = product_id;
    }

    public String getProduct_id() {
      return product_id;
    }

    public void setProduct_id(String product_id) {
      this.product_id = product_id;
    }
  }
}
