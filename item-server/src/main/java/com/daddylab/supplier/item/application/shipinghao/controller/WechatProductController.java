//package com.daddylab.supplier.item.application.shipinghao.controller;
//
//import com.daddylab.supplier.item.application.shipinghao.converter.WechatProductDetailConverter;
//import com.daddylab.supplier.item.application.shipinghao.dto.WechatAccessTokenDto;
//import com.daddylab.supplier.item.application.shipinghao.dto.WechatProductDetailDto;
//import com.daddylab.supplier.item.application.shipinghao.dto.WechatProductListDto;
//import com.daddylab.supplier.item.application.shipinghao.service.WechatApiService;
//import com.daddylab.supplier.item.application.shipinghao.service.WechatProductSyncService;
//import com.daddylab.supplier.item.domain.wechatProductDetail.gateway.WechatProductDetailGateway;
//import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.WechatProductDetail;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.web.bind.annotation.*;
//
//import java.util.List;
//
///**
// * 微信商品控制器
// *
// * <AUTHOR>
// * @date 2024-12-19
// */
//@Slf4j
//@RestController
//@RequestMapping("/api/wechat/product")
//public class WechatProductController {
//
//    @Autowired
//    private WechatProductSyncService wechatProductSyncService;
//
//    @Autowired
//    private WechatApiService wechatApiService;
//
//    @Autowired
//    private WechatProductDetailGateway wechatProductDetailGateway;
//
//    /**
//     * 获取访问令牌
//     */
//    @GetMapping("/token/{type}")
//    public WechatAccessTokenDto getAccessToken(@PathVariable String type) {
//        try {
//            return wechatApiService.getAccessToken(type);
//        } catch (Exception e) {
//            log.error("获取访问令牌异常，类型: {}", type, e);
//            return null;
//        }
//    }
//
//    /**
//     * 清除访问令牌缓存
//     */
//    @DeleteMapping("/token/cache/{type}")
//    public String clearAccessTokenCache(@PathVariable String type) {
//        try {
//            wechatApiService.clearCachedAccessToken(type);
//            return "清除访问令牌缓存成功，类型: " + type;
//        } catch (Exception e) {
//            log.error("清除访问令牌缓存异常，类型: {}", type, e);
//            return "清除访问令牌缓存失败: " + e.getMessage();
//        }
//    }
//
//    /**
//     * 全量同步商品
//     */
//    @PostMapping("/sync/all/{type}")
//    public String syncAllProducts(@PathVariable String type) {
//        try {
//            log.info("开始全量同步微信商品，类型: {}", type);
//            boolean success = wechatProductSyncService.syncAllProducts(type);
//            if (success) {
//                return "全量同步微信商品成功，类型: " + type;
//            } else {
//                return "全量同步微信商品失败，类型: " + type;
//            }
//        } catch (Exception e) {
//            log.error("全量同步微信商品异常，类型: {}", type, e);
//            return "全量同步微信商品异常: " + e.getMessage();
//        }
//    }
//
//    /**
//     * 增量同步商品
//     */
//    @PostMapping("/sync/incremental/{type}")
//    public String syncIncrementalProducts(@PathVariable String type, @RequestParam(required = false) Long lastUpdateTime) {
//        try {
//            log.info("开始增量同步微信商品，类型: {}, 上次更新时间: {}", type, lastUpdateTime);
//            boolean success = wechatProductSyncService.syncIncrementalProducts(type, lastUpdateTime);
//            if (success) {
//                return "增量同步微信商品成功，类型: " + type;
//            } else {
//                return "增量同步微信商品失败，类型: " + type;
//            }
//        } catch (Exception e) {
//            log.error("增量同步微信商品异常，类型: {}", type, e);
//            return "增量同步微信商品异常: " + e.getMessage();
//        }
//    }
//
//    /**
//     * 获取商品列表
//     */
//    @GetMapping("/list/{type}")
//    public WechatProductListDto getProductList(
//            @PathVariable String type,
//            @RequestParam(defaultValue = "0") Integer offset,
//            @RequestParam(defaultValue = "10") Integer limit) {
//        try {
//            // 限制每页数量不超过30
//            int pageSize = Math.min(limit != null ? limit : 10, 30);
//            log.info("获取商品列表，类型: {}, 请求参数: offset={}, limit={}, 实际page_size={}", type, offset, limit, pageSize);
//
//            return wechatProductSyncService.getProductList(type, offset, pageSize);
//        } catch (Exception e) {
//            log.error("获取商品列表异常，类型: {}", type, e);
//            return null;
//        }
//    }
//
//    /**
//     * 获取商品详情（优先从数据库获取）
//     */
//    @GetMapping("/detail/{type}/{productId}")
//    public WechatProductDetailDto getProductDetail(@PathVariable String type, @PathVariable String productId) {
//        try {
//            log.info("获取商品详情，类型: {}, 商品ID: {}", type, productId);
//            return wechatProductSyncService.getProductDetail(type, productId);
//        } catch (Exception e) {
//            log.error("获取商品详情异常，类型: {}, 商品ID: {}", type, productId, e);
//            return null;
//        }
//    }
//
//    /**
//     * 批量获取商品详情
//     */
//    @PostMapping("/details/{type}")
//    public List<WechatProductDetailDto> getProductDetails(@PathVariable String type, @RequestBody List<String> productIds) {
//        try {
//            log.info("批量获取商品详情，类型: {}, 商品ID数量: {}", type, productIds != null ? productIds.size() : 0);
//            return wechatProductSyncService.getProductDetails(type, productIds);
//        } catch (Exception e) {
//            log.error("批量获取商品详情异常，类型: {}", type, e);
//            return null;
//        }
//    }
//
//    /**
//     * 从数据库获取商品详情
//     */
//    @GetMapping("/db/detail/{type}/{productId}")
//    public WechatProductDetailDto getProductDetailFromDb(@PathVariable String type, @PathVariable String productId) {
//        try {
//            log.info("从数据库获取商品详情，类型: {}, 商品ID: {}", type, productId);
//            WechatProductDetail entity = wechatProductDetailGateway.findByTypeAndProductId(type, productId);
//            if (entity != null) {
//                return WechatProductDetailConverter.entityToDto(entity);
//            } else {
//                log.warn("数据库中未找到商品详情，类型: {}, 商品ID: {}", type, productId);
//                return null;
//            }
//        } catch (Exception e) {
//            log.error("从数据库获取商品详情异常，类型: {}, 商品ID: {}", type, productId, e);
//            return null;
//        }
//    }
//
//    /**
//     * 从数据库批量获取商品详情
//     */
//    @PostMapping("/db/details/{type}")
//    public List<WechatProductDetailDto> getProductDetailsFromDb(@PathVariable String type, @RequestBody List<String> productIds) {
//        try {
//            log.info("从数据库批量获取商品详情，类型: {}, 商品ID数量: {}", type, productIds != null ? productIds.size() : 0);
//            List<WechatProductDetail> entities = wechatProductDetailGateway.findByTypeAndProductIds(type, productIds);
//            if (entities != null) {
//                return WechatProductDetailConverter.entityListToDtoList(entities);
//            } else {
//                return null;
//            }
//        } catch (Exception e) {
//            log.error("从数据库批量获取商品详情异常，类型: {}", type, e);
//            return null;
//        }
//    }
//
//    /**
//     * 删除商品详情
//     */
//    @DeleteMapping("/detail/{type}/{productId}")
//    public String deleteProductDetail(@PathVariable String type, @PathVariable String productId) {
//        try {
//            log.info("删除商品详情，类型: {}, 商品ID: {}", type, productId);
//            boolean success = wechatProductDetailGateway.deleteByTypeAndProductId(type, productId);
//            if (success) {
//                return "删除商品详情成功，类型: " + type + ", 商品ID: " + productId;
//            } else {
//                return "删除商品详情失败，类型: " + type + ", 商品ID: " + productId;
//            }
//        } catch (Exception e) {
//            log.error("删除商品详情异常，类型: {}, 商品ID: {}", type, productId, e);
//            return "删除商品详情异常: " + e.getMessage();
//        }
//    }
//}