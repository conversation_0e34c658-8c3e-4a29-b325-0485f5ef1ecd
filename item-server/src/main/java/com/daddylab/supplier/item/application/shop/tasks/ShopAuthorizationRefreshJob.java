package com.daddylab.supplier.item.application.shop.tasks;

import com.daddylab.job.core.handler.annotation.XxlJob;
import com.daddylab.supplier.item.application.shop.ShopAuthorizationBizService;
import com.daddylab.supplier.item.domain.common.enums.Platform;
import com.daddylab.supplier.item.domain.messageRobot.enums.MessageRobotCode;
import com.daddylab.supplier.item.domain.winrobot.WinrobotTokenManager;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ShopAuthorization;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IShopAuthorizationService;
import com.daddylab.supplier.item.infrastructure.third.config.KuaiShouConfig;
import com.daddylab.supplier.item.infrastructure.third.config.RedBookConfig;
import com.daddylab.supplier.item.infrastructure.utils.Alert;
import com.daddylab.supplier.item.infrastructure.winrobot360.Winrobot360API;
import com.daddylab.supplier.item.infrastructure.winrobot360.types.TaskStartParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2024/4/9
 */
@Service
@Slf4j
public class ShopAuthorizationRefreshJob {

  @Autowired private IShopAuthorizationService shopAuthorizationService;

  @Autowired private RedBookConfig redBookConfig;

  @Autowired private KuaiShouConfig kuaiShouConfig;

  @Autowired private Winrobot360API winrobot360API;

  @Autowired private WinrobotTokenManager winrobotTokenManager;

  @Autowired private ShopAuthorizationBizService shopAuthorizationBizService;

  @XxlJob("PlatformAccessTokenRefreshTask:doRefresh")
  public void doRefresh() {

    final List<ShopAuthorization> authorizationsForXHS =
        shopAuthorizationService.listAuthorizationsSoonToExpire(Platform.XIAOHONGSHU, 43200);
    if (!authorizationsForXHS.isEmpty()) {
      try {
        final TaskStartParam param = new TaskStartParam();
        param.setScheduleUuid(redBookConfig.getScheduleUuid());
        winrobot360API.taskStart(winrobotTokenManager.token(), param);
      } catch (Exception e) {
        log.error("[店铺授权刷新][小红书]刷新异常", e);
      }
    }

    final List<ShopAuthorization> authorizationsForKS =
        shopAuthorizationService.listAuthorizationsSoonToExpire(Platform.KUAISHOU, 43200);
    if (!authorizationsForKS.isEmpty()) {
      try {
        final TaskStartParam param = new TaskStartParam();
        param.setScheduleUuid(kuaiShouConfig.getScheduleUuid());
        winrobot360API.taskStart(winrobotTokenManager.token(), param);
      } catch (Exception e) {
        log.error("[店铺授权刷新][快手]刷新异常", e);
      }
    }

    final List<ShopAuthorization> authorizationsForDou =
        shopAuthorizationService.listAuthorizationsSoonToExpire(Platform.DOUDIAN, 43200);
    if (!authorizationsForDou.isEmpty()) {
      for (ShopAuthorization shopAuthorization : authorizationsForDou) {
        try {
          final String authorizeResult =
              shopAuthorizationBizService.authorize(shopAuthorization.getSn());
          log.info("[店铺授权刷新]刷新结果:{}", authorizeResult);
        } catch (Exception e) {
          log.error("[店铺授权刷新]刷新异常", e);
        }
      }
    }

    final List<ShopAuthorization> authorizationsForPDD =
        shopAuthorizationService.listAuthorizationsSoonToExpire(Platform.PDD, 86400 * 3);
    if (!authorizationsForPDD.isEmpty()) {
      final String shopNoCPS =
          authorizationsForPDD.stream()
              .map(ShopAuthorization::getSn)
              .collect(Collectors.joining(","));
      Alert.text(
          MessageRobotCode.NOTICE, String.format("[店铺授权刷新][拼多多]店铺[%s]授权即将过期，请及时处理", shopNoCPS));
    }

    final List<ShopAuthorization> authorizationsForWeChatVideo =
        shopAuthorizationService.listAuthorizationsSoonToExpire(Platform.WECHAT_VIDEO, 2000);
    if (!authorizationsForDou.isEmpty()) {
      for (ShopAuthorization shopAuthorization : authorizationsForWeChatVideo) {
        try {
          final String authorizeResult =
              shopAuthorizationBizService.authorize(shopAuthorization.getSn());
          log.info("[店铺授权刷新][视频号]刷新结果:{}", authorizeResult);
        } catch (Exception e) {
          log.error("[店铺授权刷新][视频号]刷新异常", e);
        }
      }
    }
  }
}
