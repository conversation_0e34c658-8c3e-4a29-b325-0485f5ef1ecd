package com.daddylab.supplier.item.application.item.itemBatchProc;

import com.alibaba.cola.dto.PageResponse;
import com.alibaba.cola.dto.SingleResponse;
import com.daddylab.job.core.context.XxlJobHelper;
import com.daddylab.job.core.handler.annotation.XxlJob;
import com.daddylab.supplier.item.application.auth.AuthAppService;
import com.daddylab.supplier.item.application.item.ItemBizService;
import com.daddylab.supplier.item.common.ExceptionPlusFactory;
import com.daddylab.supplier.item.common.enums.ErrorCode;
import com.daddylab.supplier.item.controller.item.dto.ItemPageQuery;
import com.daddylab.supplier.item.controller.item.dto.ItemPageVo;
import com.daddylab.supplier.item.infrastructure.common.UserContext;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ItemBatchProc;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IItemBatchProcService;
import com.daddylab.supplier.item.infrastructure.utils.DateUtil;
import com.daddylab.supplier.item.infrastructure.utils.ExceptionUtil;
import com.daddylab.supplier.item.types.itemBatchProc.*;

import lombok.AccessLevel;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import org.slf4j.event.Level;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.task.TaskExecutorBuilder;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.concurrent.RejectedExecutionException;
import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReentrantLock;

/**
 * <AUTHOR>
 * @since 2023/11/13
 */
@Service
@Slf4j
public class ItemBatchProcBizServiceImpl implements ItemBatchProcBizService {
  private final ItemBizService itemBizService;
  private final IItemBatchProcService itemBatchProcService;
  private final ItemBatchProcConfig itemBatchProcConfig;
  private final List<ItemBatchProcHandler> itemBatchProcHandlers;

  @Autowired private AuthAppService authAppService;

  @Getter(value = AccessLevel.PRIVATE, lazy = true)
  private final Executor executor = initExecutor();

  public ItemBatchProcBizServiceImpl(
      ItemBizService itemBizService,
      IItemBatchProcService itemBatchProcService,
      ItemBatchProcConfig itemBatchProcConfig,
      List<ItemBatchProcHandler> itemBatchProcHandlers) {
    this.itemBizService = itemBizService;
    this.itemBatchProcService = itemBatchProcService;
    this.itemBatchProcConfig = itemBatchProcConfig;
    this.itemBatchProcHandlers = itemBatchProcHandlers;
  }

  private static ThreadPoolTaskExecutor initExecutor() {
    final ThreadPoolTaskExecutor taskExecutor =
        new TaskExecutorBuilder()
            .corePoolSize(0)
            .maxPoolSize(3)
            .queueCapacity(0)
            .threadNamePrefix("item-batch-proc")
            .build();
    taskExecutor.initialize();
    return taskExecutor;
  }

  @Override
  public SingleResponse<Long> modifyBuyer(ModifyBuyerCmd cmd) {
    checkLimit(cmd.getQuery());
    return SingleResponse.of(createTask(cmd, ItemBatchProcType.MODIFY_BUYER));
  }

  @Override
  public SingleResponse<Long> modifyWarehouse(ModifyWarehouseCmd cmd) {
    checkLimit(cmd.getQuery());
    return SingleResponse.of(createTask(cmd, ItemBatchProcType.MODIFY_WAREHOUSE));
  }

  @Override
  public SingleResponse<Long> modifyProvider(ModifyProviderCmd cmd) {
    checkLimit(cmd.getQuery());
    return SingleResponse.of(createTask(cmd, ItemBatchProcType.MODIFY_PROVIDER));
  }

  @Override
  public SingleResponse<Long> modifyStatus(ModifyStatusCmd cmd) {
    checkLimit(cmd.getQuery());
    return SingleResponse.of(createTask(cmd, ItemBatchProcType.MODIFY_STATUS));
  }

  private long createTask(Object cmd, ItemBatchProcType itemBatchProcType) {
    final long taskId = itemBatchProcService.createTask(cmd, itemBatchProcType);
    scheduleTasks();
    return taskId;
  }

  private void checkLimit(ItemPageQuery query) {
    query.setNotQueryStock(true);
    query.setPageSize(1);
    final PageResponse<ItemPageVo> itemPageVoPageResponse = itemBizService.queryPage(query);
    if (itemPageVoPageResponse.getTotalCount() == 0) {
      throw ExceptionPlusFactory.bizException(ErrorCode.OPERATION_REJECT, "当前条件选中的商品数量为0，请调整条件");
    }
    if (itemPageVoPageResponse.getTotalCount() > itemBatchProcConfig.getMaxNumLimit()) {
      throw ExceptionPlusFactory.bizException(
          ErrorCode.OPERATION_REJECT, "当前条件选择的商品总数过多，请调整条件缩小选择范围后重试");
    }
  }

  private final Lock scheduleTasksLock = new ReentrantLock();

  @XxlJob("ItemBatchProcBizService:scheduleTasks")
  @Override
  public void scheduleTasks() {
    if (scheduleTasksLock.tryLock()) {
      try {
        for (ItemBatchProc itemBatchProc :
            itemBatchProcService
                .lambdaQuery()
                .in(
                    ItemBatchProc::getStatus,
                    Arrays.asList(
                        ItemBatchProcStatus.TO_BE_EXECUTED, ItemBatchProcStatus.UNDER_EXECUTION))
                .list()) {
          try {
            CompletableFuture.runAsync(() -> handleWithUserContext(itemBatchProc), getExecutor());
          } catch (RejectedExecutionException e) {
            log.debug("【ItemBatchProc】处理队列已满，终止程序");
            break;
          }
        }
      } catch (Exception e) {
        log.error("【ItemBatchProc】任务调度异常:{}", e.getMessage(), e);
        XxlJobHelper.handleFail(ExceptionUtil.stacktraceToString(e));
      } finally {
        scheduleTasksLock.unlock();
      }
    }
  }

  private void handleWithUserContext(ItemBatchProc itemBatchProc) {
    authAppService.loadUserContext(itemBatchProc.getCreatedUid(), false);
    try {
      handle(itemBatchProc);
    } finally {
      UserContext.remove();
    }
  }

  private void handle(ItemBatchProc itemBatchProc) {
    final Long id = itemBatchProc.getId();
    final Long currentTime = DateUtil.currentTime();
    final long lockUtil = currentTime + itemBatchProcConfig.getTimeout();
    if (Objects.equals(itemBatchProc.getStatus(), ItemBatchProcStatus.UNDER_EXECUTION.getValue())) {
      final boolean isUnLock =
          itemBatchProc.getLockUntil() == 0L || currentTime >= itemBatchProc.getLockUntil();
      if (isUnLock && itemBatchProcService.lock(id, lockUtil)) {
        itemBatchProcService.retry(id);
        log.warn("【ItemBatchProc】超时重试 id={}", id);
      } else {
        return;
      }
    } else if (Objects.equals(
        itemBatchProc.getStatus(), ItemBatchProcStatus.TO_BE_EXECUTED.getValue())) {
      if (!itemBatchProcService.setProcessingStatus(id, lockUtil)) {
        return;
      }
      log.debug("【ItemBatchProc】锁定成功 id={}", id);
    }
    log.info("【ItemBatchProc】开始执行 id={}", id);
    final Optional<ItemBatchProcHandler> handler =
        itemBatchProcHandlers.stream()
            .filter(factory -> factory.isSupport(itemBatchProc))
            .findFirst();
    handler.ifPresent(
        factory -> {
          try {
            factory.handle(itemBatchProc, new SynchronizeImpl(id));
            log.info("【ItemBatchProc】执行完成");
            itemBatchProcService.setCompleteStatus(id, true, "success");
          } catch (Exception e) {
            log.error("【ItemBatchProc】执行异常:{} id={}", e.getMessage(), id, e);
            itemBatchProcService.setCompleteStatus(
                id, false, ExceptionUtil.stacktraceToOneLineString(e));
          }
        });
    if (!handler.isPresent()) {
      log.error("【ItemBatchProc】未注册实现类 id={}", id);
      itemBatchProcService.setCompleteStatus(id, false, "未注册实现类");
    }
  }

  @RequiredArgsConstructor
  private class SynchronizeImpl implements ItemBatchProcHandler.Synchronize {
    private final Long id;

    @Override
    public void log(Level level, String msg, Object... vars) {
      itemBatchProcService.log(id, level, msg, vars);
    }

    @Override
    public void setProcess(int process) {
      log.info("【ItemBatchProc】更新进度 {}", process);
      itemBatchProcService.setProcess(id, process);
    }
  }
}
