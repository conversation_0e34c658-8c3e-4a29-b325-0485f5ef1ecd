package com.daddylab.supplier.item.application.warehouse;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Assert;
import com.alibaba.cola.dto.MultiResponse;
import com.alibaba.cola.dto.SingleResponse;
import com.daddylab.supplier.item.application.afterSaleLink.AfterSaleShareLinkBizService;
import com.daddylab.supplier.item.application.afterSalesForwarding.types.AfterSalesForwardingShareWarehouseCmd;
import com.daddylab.supplier.item.domain.user.gateway.UserGateway;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.AfterSaleShareLink;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.Warehouse;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IWarehouseService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.user.StaffInfo;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.github.benmanes.caffeine.cache.LoadingCache;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

/**
 * <AUTHOR>
 * @since 2022/4/22
 */
@Service
public class WarehouseGatewayImpl implements WarehouseGateway {

  public static final int CACHE_SECONDS = 60 * 30;
  public static final int CACHE_MAXIMUM_SIZE = 100;

  @Autowired IWarehouseService warehouseService;

  @Autowired
  @Qualifier("UserGatewayCacheImpl")
  UserGateway userGateway;

  @Autowired AfterSaleShareLinkBizService afterSaleShareLinkBizService;

  LoadingCache<String, Optional<Warehouse>> warehouseLoadingCache =
      Caffeine.newBuilder()
          .expireAfterAccess(CACHE_SECONDS, TimeUnit.SECONDS)
          .maximumSize(CACHE_MAXIMUM_SIZE)
          .build(this::getWarehouse0);

  LoadingCache<String, List<Warehouse>> allWarehouseLoadingCache =
      Caffeine.newBuilder()
          .expireAfterAccess(60, TimeUnit.SECONDS)
          .build(k -> warehouseService.list());

  @Override
  public Optional<Long> warehouseId(String warehouseNo) {
    return cacheGetWarehouse(warehouseNo, false).map(Warehouse::getId);
  }

  @Override
  public Optional<Warehouse> getWarehouse(String warehouseNo) {
    if (StringUtils.isEmpty(warehouseNo)) return Optional.empty();
    warehouseLoadingCache.invalidate(warehouseNo);
    return warehouseLoadingCache.get(warehouseNo);
  }

  private Optional<Warehouse> getWarehouse0(String warehouseNo) {
    return warehouseService.lambdaQuery().eq(Warehouse::getNo, warehouseNo).select().oneOpt();
  }

  @Override
  public Optional<Warehouse> cacheGetWarehouse(String warehouseNo, boolean refresh) {
    return Optional.ofNullable(warehouseNo)
        .flatMap(k -> Optional.ofNullable(warehouseLoadingCache.get(k)).orElseGet(Optional::empty));
  }

  @Override
  public List<Warehouse> getWarehouseListByOrderPersonnelIds(List<Long> orderPersonnelIds) {
    if (CollUtil.isEmpty(orderPersonnelIds)) return Collections.emptyList();
    return Objects.requireNonNull(allWarehouseLoadingCache.get(""), "仓库缓存加载失败").stream()
        .filter(v -> v.selectOrderPersonnelIds().stream().anyMatch(orderPersonnelIds::contains))
        .collect(Collectors.toList());
  }

  @Override
  public List<Warehouse> getWarehouseListByNos(List<String> warehouseNos) {
    if (CollUtil.isEmpty(warehouseNos)) return Collections.emptyList();
    return Objects.requireNonNull(allWarehouseLoadingCache.get(""), "仓库缓存加载失败").stream()
        .filter(v -> warehouseNos.contains(v.getNo()))
        .collect(Collectors.toList());
  }

  @Override
  public Map<String, String> getOrderPersonnelByNames(Collection<String> names) {
    List<Warehouse> list =
        warehouseService
            .lambdaQuery()
            .in(Warehouse::getName, names)
            .eq(Warehouse::getState, 1)
            .eq(Warehouse::getIsVirtualWarehouse, 0)
            .list();
    return list.stream()
        .collect(
            Collectors.toMap(
                Warehouse::getName,
                warehouse -> {
                  String orderPersonnel = warehouse.getOrderPersonnel();
                  if (StringUtils.hasText(orderPersonnel)) {
                    List<Long> userIds =
                        Arrays.stream(orderPersonnel.split(",."))
                            .map(Long::valueOf)
                            .distinct()
                            .collect(Collectors.toList());
                    Map<Long, StaffInfo> longStaffInfoMap =
                        userGateway.batchQueryStaffInfoByIds(userIds);
                    return longStaffInfoMap.values().stream()
                        .map(StaffInfo::getNickname)
                        .collect(Collectors.joining(","));
                  }
                  return "";
                }));
  }

  @Override
  public MultiResponse<Warehouse> queryWarehouseByName(String warehouseName) {
    List<Warehouse> list =
        warehouseService
            .lambdaQuery()
            .like(
                org.apache.commons.lang3.StringUtils.isNotBlank(warehouseName),
                Warehouse::getName,
                warehouseName)
            .orderByDesc(Warehouse::getId)
            .last("limit 99")
            .list();
    return MultiResponse.of(list);
  }

  @Override
  public MultiResponse<Warehouse> shareQueryWarehouseByName(
      AfterSalesForwardingShareWarehouseCmd warehouseCmd) {
    final SingleResponse<AfterSaleShareLink> checkTokenResponse =
        afterSaleShareLinkBizService.checkToken(warehouseCmd.getToken());
    Assert.state(checkTokenResponse.isSuccess(), checkTokenResponse.getErrMessage());
    String name = warehouseCmd.getName();
    List<Warehouse> list =
        warehouseService
            .lambdaQuery()
            .like(org.apache.commons.lang3.StringUtils.isNotBlank(name), Warehouse::getName, name)
            .orderByDesc(Warehouse::getId)
            .last("limit 99")
            .list();
    return MultiResponse.of(list);
  }
}
