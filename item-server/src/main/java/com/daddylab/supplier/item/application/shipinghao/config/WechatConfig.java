package com.daddylab.supplier.item.application.shipinghao.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * 微信配置类
 * 
 * <AUTHOR>
 * @date 2024-12-19
 */
@Data
@Component
@ConfigurationProperties(prefix = "wechat.shop")
public class WechatConfig {
    
    /**
     * 微信小店配置映射
     * key: type (daddylab/member)
     * value: 对应的appId和appSecret
     */
    private Map<String, WechatShopConfig> shops;

  /**
   * 获取访问令牌URL
   */
  private String accessTokenUrl = "https://api.weixin.qq.com/cgi-bin/stable_token?";

    /**
     * 获取商品列表URL
     */
    private String productListUrl = "https://api.weixin.qq.com/channels/ec/product/list/get";
    
    /**
     * 获取商品详情URL
     */
    private String productDetailUrl = "https://api.weixin.qq.com/channels/ec/product/get";
    
    /**
     * HTTP连接超时时间（毫秒）
     */
    private Integer connectTimeout = 10000;
    
    /**
     * HTTP读取超时时间（毫秒）
     */
    private Integer readTimeout = 30000;
    
    /**
     * 获取指定类型的配置
     */
    public WechatShopConfig getShopConfig(String type) {
        if (shops != null && shops.containsKey(type)) {
            return shops.get(type);
        }
        return null;
    }
    
    /**
     * 获取所有配置的类型
     */
    public String[] getShopTypes() {
        if (shops != null) {
            return shops.keySet().toArray(new String[0]);
        }
        return new String[0];
    }
    
    /**
     * 微信小店配置
     */
    @Data
    public static class WechatShopConfig {
        private String appId;
        private String appSecret;
    }
} 