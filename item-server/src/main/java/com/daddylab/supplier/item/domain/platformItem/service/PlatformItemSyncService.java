package com.daddylab.supplier.item.domain.platformItem.service;

import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.PlatformItem;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.PlatformItemSku;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.WdtPlatformGoods;

import java.util.Map;

/**
 * <AUTHOR>
 * @since 2022/1/6
 */
public interface PlatformItemSyncService {

    Map<String, SyncResult> syncWdtPlatformGoodsByItemId(Long itemId);

    /**
     * 同步旺店通平台商品数据
     *
     * @param wdtPlatformGoods 数据模型
     * @return SyncStatus
     */
    SyncResult syncWdtPlatformGoods(WdtPlatformGoods wdtPlatformGoods);

    /**
     * 同步指定外部平台ID的商品数据
     * @param outerItemId 外部平台商品ID
     * @return 同步结果
     */
    Map<String, SyncResult> syncWdtPlatformGoods(String outerItemId);

    /**
     * 匹配商品并保存
     * @param platformItem 平台商品
     * @param platformItemSku 平台商品SKU
     */
    void matchAndSave(PlatformItem platformItem, PlatformItemSku platformItemSku);

    void matchItem(PlatformItem platformItem, PlatformItemSku platformItemSku);

    void updatePlatformItemStat(Long platformItemId);

    int deleteInvalidPlatformItem(String shopNo, Long invalidTime);
}
