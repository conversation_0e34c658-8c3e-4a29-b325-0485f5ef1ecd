package com.daddylab.supplier.item.application.shipinghao.dto.wechatProduct;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List; /** 商品信息 */
@Data
public class WeChatProduct {
  /** 商品SPU ID */
  @JsonProperty("product_id")
  private String productId;

  /** 外部商品ID（第三方商品ID） */
  @JsonProperty("out_product_id")
  private String outProductId;

  /** 商品标题 */
  private String title;

  /** 商品子标题 */
  @JsonProperty("sub_title")
  private String subTitle;

  /** 商品主图列表，建议尺寸800x800px */
  @JsonProperty("head_imgs")
  private List<String> headImgs;

  /** 商品详情信息 */
  @JsonProperty("desc_info")
  private DescInfo descInfo;

  /** 商品类目列表 */
  private List<Category> cats;

  /** 商品属性列表（规格参数） */
  private List<Attribute> attrs;

  /** 快递信息 */
  @JsonProperty("express_info")
  private ExpressInfo expressInfo;

  /** 商品状态：1-待审核，2-审核中，3-审核失败，4-审核成功，5-上架，6-下架，11-自主下架 */
  private Integer status;

  /** 商品SKU列表 */
  private List<Sku> skus;

  /** 商品最低价格，单位：分 */
  @JsonProperty("min_price")
  private Integer minPrice;

  /** SPU编码 */
  @JsonProperty("spu_code")
  private String spuCode;

  /** 发货方式：0-现货发货，1-预约发货 */
  @JsonProperty("deliver_method")
  private Integer deliverMethod;

  /** 售后描述 */
  @JsonProperty("aftersale_desc")
  private String aftersaleDesc;

  /** 限购信息 */
  @JsonProperty("limited_info")
  private LimitedInfo limitedInfo;

  /** 品牌ID */
  @JsonProperty("brand_id")
  private String brandId;

  /** 商品资质列表 */
  private List<String> qualifications;

  /** 额外服务信息 */
  @JsonProperty("extra_service")
  private ExtraService extraService;

  /** 商品类型：1-实体商品，2-虚拟商品 */
  @JsonProperty("product_type")
  private Integer productType;

  /** 编辑时间戳 */
  @JsonProperty("edit_time")
  private Long editTime;

  /** 售后信息 */
  @JsonProperty("after_sale_info")
  private AfterSaleInfo afterSaleInfo;

  /** 是否在橱窗中隐藏：0-不隐藏，1-隐藏 */
  @JsonProperty("hide_in_window")
  private Integer hideInWindow;

  /** 商品资质信息列表 */
  @JsonProperty("product_qua_infos")
  private List<ProductQuaInfo> productQuaInfos;

  /** 尺码表信息 */
  @JsonProperty("size_chart")
  private SizeChart sizeChart;

  /** 发货账户类型列表 */
  @JsonProperty("deliver_acct_type")
  private List<String> deliverAcctType;

  /** 域类型：0-普通，1-特殊 */
  @JsonProperty("domain_type")
  private Integer domainType;

  /** 自定义配置 */
  @JsonProperty("custom_config")
  private CustomConfig customConfig;

  /** 商品简短标题 */
  @JsonProperty("short_title")
  private String shortTitle;

  /** 累计销售数量 */
  @JsonProperty("total_sold_num")
  private Integer totalSoldNum;

  /** 新版类目列表（多级类目结构） */
  @JsonProperty("cats_v2")
  private List<Category> catsV2;
}
