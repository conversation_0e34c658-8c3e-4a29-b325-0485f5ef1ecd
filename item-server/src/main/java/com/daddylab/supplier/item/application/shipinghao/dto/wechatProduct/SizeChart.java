package com.daddylab.supplier.item.application.shipinghao.dto.wechatProduct;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

/**
 * 尺码表信息
 */
@Data
public class SizeChart {
  /** 尺码表ID */
  @JsonProperty("size_chart_id")
  private String sizeChartId;

  /** 尺码表名称 */
  private String name;

  /** 规格列表 */
  @JsonProperty("specification_list")
  private List<SpecificationInfo> specificationList;

  /**
   * 规格信息
   */
  @Data
  public static class SpecificationInfo {
    /** 规格名称 */
    private String name;

    /** 是否为范围值 */
    @JsonProperty("is_range")
    private Boolean isRange;

    /** 规格值列表 */
    @JsonProperty("value_list")
    private List<ValueInfo> valueList;
  }

  /**
   * 规格值信息
   */
  @Data
  public static class ValueInfo {
    /** 键 */
    private String key;

    /** 值 */
    private String value;

    /** 左边界（范围值时使用） */
    private String left;

    /** 右边界（范围值时使用） */
    private String right;
  }
}
