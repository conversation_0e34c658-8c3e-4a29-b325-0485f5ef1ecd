package com.daddylab.supplier.item.application.shipinghao.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

/**
 * 微信商品列表DTO
 * 
 * <AUTHOR>
 * @date 2024-12-19
 */
@Data
public class WechatProductListDto {
    
    /**
     * 商品ID列表
     */
    @JsonProperty("product_ids")
    private List<String> productIds;
    
    /**
     * 下一页的key
     */
    @JsonProperty("next_key")
    private String nextKey;
    
    /**
     * 总数量
     */
    @JsonProperty("total_num")
    private Integer totalNum;
    

    /**
     * 错误码
     */
    @JsonProperty("errcode")
    private Integer errcode;

    /**
     * 错误信息
     */
    @JsonProperty("errmsg")
    private String errmsg;


    public boolean isSuccess() {
        return errcode == null || errcode == 0;
    }
} 