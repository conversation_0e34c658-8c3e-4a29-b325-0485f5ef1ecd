package com.daddylab.supplier.item.controller.test;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.cola.dto.MultiResponse;
import com.alibaba.cola.dto.Response;
import com.alibaba.cola.dto.SingleResponse;
import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.daddylab.ark.sailor.item.common.base.vo.Result;
import com.daddylab.supplier.item.application.drawer.impl.copySupport.ItemTrainingMaterialsCopyServiceImpl;
import com.daddylab.supplier.item.application.item.ItemBizService;
import com.daddylab.supplier.item.application.platformItemSkuInventory.PlatformItemSkuSyncService;
import com.daddylab.supplier.item.application.purchase.order.factory.CommonUtil;
import com.daddylab.supplier.item.application.user.UserBizService;
import com.daddylab.supplier.item.common.enums.PoolEnum;
import com.daddylab.supplier.item.common.util.ThreadUtil;
import com.daddylab.supplier.item.controller.item.dto.ExportCmd;
import com.daddylab.supplier.item.domain.common.enums.Platform;
import com.daddylab.supplier.item.domain.user.gateway.UserGateway;
import com.daddylab.supplier.item.infrastructure.authorize.Auth;
import com.daddylab.supplier.item.infrastructure.config.RefreshConfig;
import com.daddylab.supplier.item.infrastructure.doudian.DouDianCommon;
import com.daddylab.supplier.item.infrastructure.doudian.DouDianSyncJob;
import com.daddylab.supplier.item.infrastructure.email.EmailService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.custom.CascadedDivisionLevel;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.BizLevelDivision;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ItemSku;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.BizUnionTypeEnum;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.DivisionLevelEnum;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.DivisionLevelValueEnum;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IItemDrawerService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IItemSkuService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.impl.BizLevelDivisionServiceImpl;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.feign.ArkSailorItemFeignClient;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.feign.dto.ErpItemSkuUpdateStockParam;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.feign.dto.ItemSkuPageQuery;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.feign.dto.SkuPageListItemVO;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.feign.dto.SkuStockVo;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.feign.enums.StockTypeEnum;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.user.StaffInfo;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.user.StaffListQuery;
import com.daddylab.supplier.item.infrastructure.kingdee.dto.KingDeeSkuResp;
import com.daddylab.supplier.item.infrastructure.kingdee.util.ReqTemplate;
import com.daddylab.supplier.item.infrastructure.limit.RateLimit;
import com.daddylab.supplier.item.infrastructure.utils.JsonUtil;
import com.doudian.open.api.material_createFolder.MaterialCreateFolderRequest;
import com.doudian.open.api.material_createFolder.MaterialCreateFolderResponse;
import com.doudian.open.api.material_createFolder.param.MaterialCreateFolderParam;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiOperation;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.flowable.engine.ProcessEngine;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.inject.Inject;
import javax.inject.Named;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;

import static com.daddylab.supplier.item.common.GlobalConstant.SEVEN_UP;

@Slf4j
@RestController
@RequestMapping("/test")
@Api(hidden = true)
public class TestController {
    @Inject
    UserBizService userBizService;

    @Autowired
    private UserGateway userGateway;

    @Inject
    @Named("UserGatewayCacheImpl")
    private UserGateway userGatewayCacheImpl;

    @Autowired
    RefreshConfig refreshConfig;

    @Autowired
    ItemBizService itemBizService;

    @Resource
    DouDianSyncJob douDianSyncJob;
    @Autowired
    private ArkSailorItemFeignClient arkSailorItemFeignClient;

//    @Autowired
//    private HelloService helloService;

//    @PostMapping(value = "/test")
//    @Auth(noAuth = true)
//    public Response test() {
//        final String hello = helloService.hello("i am supplier-item");
//        log.info("hello reply: {}", hello);
//        return Response.buildSuccess();
//    }

    @GetMapping(value = "/fresh")
    @Auth(noAuth = true)
    public SingleResponse<String> nacosFreshTest() {
        return SingleResponse.of(refreshConfig.getWhiteUri());
    }

    @Data
    public static class TestData {
        LocalDateTime date;
        Integer status;
    }

    @Resource
    IItemSkuService iItemSkuService;

    @Resource
    ReqTemplate reqTemplate;

    @PostMapping(value = "/kingDeeTest", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    @ApiOperation("金蝶数据各种洗")
    @Auth(noAuth = true)
    public Response kingDeeTest() {
        ThreadUtil.execute(PoolEnum.COMMON_POOL, () -> {

            List<String> emptyCode = new LinkedList<>();
            List<String> errorCode = new LinkedList<>();
            AtomicInteger count = new AtomicInteger();
            iItemSkuService.lambdaQuery().eq(ItemSku::getKingDeeId, "")
                    .select().list()
                    .forEach(itemSku -> {
                        try {
                            Optional<KingDeeSkuResp> kingDeeSkuResp = reqTemplate.querySkuByNo(itemSku.getSkuCode());
                            if (kingDeeSkuResp.isPresent()) {
                                itemSku.setKingDeeId(kingDeeSkuResp.get().getId());
                                iItemSkuService.updateById(itemSku);
                            } else {
                                emptyCode.add(itemSku.getSkuCode());
                            }
                        } catch (Exception e) {
                            log.error("查询sku金蝶数据异常", e);
                            errorCode.add(itemSku.getSkuCode());
                        } finally {
                            count.getAndIncrement();
                            log.info("已完成数量:{}", count.get());
                        }
                    });
            if (CollUtil.isNotEmpty(emptyCode)) {
                log.error("查询为空code:{}", JsonUtil.toJson(emptyCode));
            }
            if (CollUtil.isNotEmpty(errorCode)) {
                log.error("查询为空code:{}", JsonUtil.toJson(errorCode));
            }

        });
        return Response.buildSuccess();

    }


    @GetMapping("/userInfo")
    @Auth(noAuth = true)
    public SingleResponse<StaffInfo> getUserInfo(Long userId) {
        final StaffInfo staffInfo = userGateway.queryStaffInfoById(userId);
        return SingleResponse.of(staffInfo);
    }

    @GetMapping("/userInfoByQuery")
    @Auth(noAuth = true)
    public MultiResponse<StaffInfo> userInfoByQuery(@RequestBody StaffListQuery query) {
        final List<StaffInfo> staffInfo = userGateway.queryStaffList(query);
        return MultiResponse.of(staffInfo);
    }

    @Autowired
    EmailService emailService;

    @GetMapping("/testMail")
    @Auth(noAuth = true)
    public SingleResponse<Boolean> testMail(String emailAddress, String content) {
        emailService.sendUrlMail(emailAddress, "demo" + DateUtil.date().toDateStr(), content, "http://www.baidu.com");
        return SingleResponse.of(true);
    }

    @PostMapping(value = "/exportItem")
    @Auth(noAuth = true)
    public SingleResponse<Boolean> exportItem(@RequestBody ExportCmd cmd) {
        Response response = itemBizService.exportItem(cmd);
        return SingleResponse.of(true);
    }

    @Resource
    DouDianCommon common;

    @GetMapping(value = "/createdDouDianFolder")
    @Auth(noAuth = true)
    public SingleResponse<MaterialCreateFolderResponse> createdDouDianFolder() {
        MaterialCreateFolderRequest request = new MaterialCreateFolderRequest();
        MaterialCreateFolderParam param = request.getParam();
        param.setName("erp同步图片");
        param.setParentFolderId("0");
        param.setType(0);
        MaterialCreateFolderResponse response = request.execute(common.getAccessToken());
        return SingleResponse.of(response);
    }


    @GetMapping(value = "/douDianSyncJob")
    @Auth(noAuth = true)
    public Response douDianSyncJob() {
        douDianSyncJob.run();
        return Response.buildSuccess();
    }

    @GetMapping(value = "/test1698306297")
    @Auth(noAuth = true)
    public Response test1698306297() {

        final ProcessEngine processEngine = SpringUtil.getBean(ProcessEngine.class);
        processEngine.getTaskService().setAssignee("88fbf1d2-006e-11ef-be26-3a197c8e8d31", "test" + RandomUtil.randomInt());
        return Response.buildSuccess();
    }

    @GetMapping(value = "/test88")
    @Auth(noAuth = true)
    public Response test88() {

        final ItemTrainingMaterialsCopyServiceImpl bean = SpringUtil.getBean(ItemTrainingMaterialsCopyServiceImpl.class);
        final IItemDrawerService itemDrawerService = SpringUtil.getBean(IItemDrawerService.class);
        final RuntimeException notFound = new RuntimeException("not found");
        bean.handler(itemDrawerService.getByItemId(363505L).orElseThrow(() -> notFound),
                itemDrawerService.getByItemId(363546L).orElseThrow(() -> notFound));
        return Response.buildSuccess();
    }


//    @SneakyThrows
//    @ApiOperation("kingDeeProvider")
//    @RequestMapping(value = "/kingDeeProvider", method = RequestMethod.POST)
//    @ResponseBody
//    @Auth(noAuth = true)
//    public Response kingDeeProvider() {
//        ThreadUtil.execute(PoolEnum.COMMON_POOL, () -> {
//            QuerySkuProviderJob bean = SpringUtil.getBean(QuerySkuProviderJob.class);
//            try {
//                bean.querySkuProviderJob();
//            } catch (Exception e) {
//                throw new RuntimeException(e);
//            }
//        });
//        return Response.buildSuccess();
//    }

    @GetMapping(value = "/test1")
    @Auth(noAuth = true)
    public SingleResponse<Map<String, Object>> test1(@RequestParam("id") String itemId, @RequestParam("skuId") String skuId) {
        BizLevelDivisionServiceImpl bean = SpringUtil.getBean(BizLevelDivisionServiceImpl.class);
    //        bean.saveCascadedLevels(
    //            BizUnionTypeEnum.SPU,
    //            363679L,
    //            "363679",
    //            Arrays.asList(
    //                new CascadedDivisionLevel(
    //                    DivisionLevelValueEnum.C_E_COMMERCE,
    //                    Arrays.asList(
    //                        new CascadedDivisionLevel(DivisionLevelValueEnum.B_MERCHANT_ENTER)))),
    //            Arrays.asList(DivisionLevelEnum.BUSINESS_TYPE, DivisionLevelEnum.COOPERATION));
        bean.savePlainLevels(
            BizUnionTypeEnum.COMBINATION,
            2582L,
            Arrays.asList(DivisionLevelValueEnum.C_E_COMMERCE),
            Arrays.asList(DivisionLevelEnum.BUSINESS_TYPE, DivisionLevelEnum.COOPERATION));
        return SingleResponse.of(null);

//        Map<String, Object> objectObjectHashMap = MapUtil.newHashMap();
//        // 获取商品列表
//        SearchItemListResponse itemPageList = redBookService.getItemPageList(RedBookItemPageQuery.of(1, 20));
//        objectObjectHashMap.put("itemPageList", itemPageList);
//        //获取sku列表
//        GetDetailSkuListResponse skuPageList = redBookService.getSkuPageList(SkuPageQuery.of(1, 10));
//        objectObjectHashMap.put("skuList", skuPageList);
//        SkuPageQuery of = SkuPageQuery.of(skuId);
//        GetDetailSkuListResponse skuPageList1 = redBookService.getSkuPageList(of);
//        objectObjectHashMap.put("skuList1", skuPageList1);
//        //获取商品详情
//        GetItemInfoResponse itemInfo = redBookService.getItemInfo(itemId);
//        objectObjectHashMap.put("itemInfo", itemInfo);
//        //获取商品库存
//        String currentSkuId = itemInfo.getSkuInfos().get(0).getId();
//        SkuStockResponse skuStock = redBookService.getSkuStock(currentSkuId);
//        objectObjectHashMap.put("skuStock", skuStock);
//        if (skuStock.getSkuStock().getTotal() == 0) {
//            // 同步库存
//            SkuStockResponse skuStockResponse = redBookService.syncSkuStock(currentSkuId, 20);
//            objectObjectHashMap.put("syncSkuStock", skuStockResponse);
//            // 增减库存
//            SkuStockResponse skuStockResponse1 = redBookService.incSkuStock(currentSkuId, -5);
//            objectObjectHashMap.put("incSkuStock", skuStockResponse1);
//        }
//        return SingleResponse.of(objectObjectHashMap);
    }

    @GetMapping(value = "/test2")
    @Auth(noAuth = true)
    public SingleResponse<Map<String, Object>> test2(@RequestParam("id") Long itemId) {
        return SingleResponse.of(null);
//        Map<String, Object> objectObjectHashMap = MapUtil.newHashMap();
//        //获取商品列表
//        GetItemListResponseParam itemList = kuaiShouService.getItemList(KsItemPageQuery.of(1, 10));
//        objectObjectHashMap.put("itemList", itemList);
//        //获取商品详情
//        ItemGetResponseParam itemDetail = kuaiShouService.getItemDetail(itemId);
//        objectObjectHashMap.put("itemDetail", itemDetail);
//
//        SkuInfoResponseParam skuDetail = kuaiShouService.getSkuDetail(itemId, itemDetail.getSkuInfos()[0].getRelSkuId());
//        objectObjectHashMap.put("skuDetail", skuDetail);
//        if (Objects.nonNull(itemDetail)) {
//            SkuInfoResponseParam skuInfo = itemDetail.getSkuInfos()[0];
//            if (skuInfo.getSkuStock() == 0L) {
//                //增加库存
//                kuaiShouService.skuStockUpdate(itemId, skuInfo.getKwaiSkuId(), StockChangeTypeEnum.INCREASE, 10);
//                //减少库存
//                kuaiShouService.skuStockUpdate(itemId, itemDetail.getSkuInfos()[0].getKwaiSkuId(), StockChangeTypeEnum.DECREASE, 5);
//            }
//        }
//
//        return SingleResponse.of(objectObjectHashMap);
    }


    @GetMapping(value = "/test3")
    @Auth(noAuth = true)
    public SingleResponse<Map<String, Object>> test3(@RequestParam("skuId") Long skuId) {
        Map<String, Object> resultMap = MapUtil.newHashMap();
        Result<SkuStockVo> skuStock = arkSailorItemFeignClient.getSkuStock(skuId);
        // 单个查询
        resultMap.put("skuStock", skuStock.getData());
        // 批量查询
        Result<List<SkuStockVo>> skuStocks = arkSailorItemFeignClient.getSkuStocks(ListUtil.of(skuId));
        resultMap.put("skuStockList", skuStocks.getData());
        // 增加库存
        arkSailorItemFeignClient.updateStock(ErpItemSkuUpdateStockParam.of(skuId, StockTypeEnum.INCREASE, 10));
        Result<SkuStockVo> increaseStock = arkSailorItemFeignClient.getSkuStock(skuId);
        resultMap.put("increaseStock", increaseStock.getData());
        // 较少库存
        arkSailorItemFeignClient.updateStock(ErpItemSkuUpdateStockParam.of(skuId, StockTypeEnum.DECREASE, 5));
        Result<SkuStockVo> decreaseStock = arkSailorItemFeignClient.getSkuStock(skuId);
        resultMap.put("decreaseStock", decreaseStock.getData());
        Result<Page<SkuPageListItemVO>> skuPageList = arkSailorItemFeignClient.getSkuPageList(ItemSkuPageQuery.of(1L, 20L));
        resultMap.put("skuPageList", skuPageList.getData());
        return SingleResponse.of(resultMap);
    }

    @Data
    @ApiModel("平台商品同步命令")
    public class PlatformItemSyncCmd {
        @ApiModelProperty("平台商品ID（外）")
        @NotBlank
        private String outerItemId;

        @ApiModelProperty("平台")
        @NotNull
        private Platform platform;

        @ApiModelProperty("店铺编号")
        @NotNull
        private String shopNo;
    }

    @PostMapping(value = "/test4")
    @Auth(noAuth = true)
    public void test4(@RequestBody String params) {

        final Map<String, PlatformItemSkuSyncService> syncServices = SpringUtil.getBeansOfType(PlatformItemSkuSyncService.class);
        final PlatformItemSyncCmd platformItemSyncStockCmd = JSON.parseObject(params,
                PlatformItemSyncCmd.class);
        for (PlatformItemSkuSyncService syncService : syncServices.values()) {
            if (syncService.defaultType() == platformItemSyncStockCmd.getPlatform()) {
                syncService.syncItem(platformItemSyncStockCmd.getShopNo(), platformItemSyncStockCmd.getOuterItemId());
            }
        }
    }

    @PostMapping(value = "/test5")
    @Auth(noAuth = true)
    public void test5() {

        final Map<String, PlatformItemSkuSyncService> syncServices = SpringUtil.getBeansOfType(PlatformItemSkuSyncService.class);
        for (PlatformItemSkuSyncService syncService : syncServices.values()) {
            if (syncService.defaultType() == Platform.LAOBASHOP) {
                syncService.fullDoseSync();
            }
        }
    }

    @PostMapping(value = "/test888")
    @Auth(noAuth = true)
    public void test888() {
        final ProcessEngine bean = SpringUtil.getBean(ProcessEngine.class);
        bean.getTaskService().setAssignee("c3b910c0-b937-11ef-9e63-c646b0baadd4", "7505357");
    }


    @GetMapping(value = "/testLimit")
    @Auth(noAuth = true)
    @RateLimit(key = "testLimit", methodDesc = "测试数据", time = 30, count = 1)
    public SingleResponse<String> testLimit() {
        final CommonUtil commonUtil = SpringUtil.getBean(CommonUtil.class);
        commonUtil.remainWithUrl("202401",
                "http://http://api4j.dlab.cn/supplier/item/test/testLimit?",
                ListUtil.of(SEVEN_UP), "点击此处链接，测试。");
        return SingleResponse.of("testLimit");
    }

}
