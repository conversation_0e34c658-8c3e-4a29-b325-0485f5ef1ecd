package com.daddylab.supplier.item.application.platformItemSkuInventory.support;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.daddylab.ark.sailor.item.common.base.vo.Result;
import com.daddylab.supplier.item.application.platformItemSkuInventory.domain.params.PlatformSkuSyncInfo;
import com.daddylab.supplier.item.domain.common.enums.Platform;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.feign.ArkSailorItemFeignClient;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.feign.dto.ItemSkuPageQuery;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.feign.dto.SkuPageListItemVO;
import com.daddylab.supplier.item.infrastructure.utils.DateUtil;
import com.daddylab.supplier.item.infrastructure.utils.StringUtil;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.core.BatchStatus;
import org.springframework.batch.item.ItemProcessor;
import org.springframework.batch.item.ItemReader;
import org.springframework.batch.item.ItemWriter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @class MallItemSkuInventoryServiceImpl.java
 * @description 描述类的作用
 * @date 2024-03-15 14:07
 */
@Service
@Slf4j
public class MallItemSkuInventoryServiceImpl extends
        AbstractPlatformItemSkuSync<SkuPageListItemVO, PlatformSkuSyncInfo> {
    
    @Autowired
    private ArkSailorItemFeignClient itemFeignClient;
    private int pageSize = 500;
    
    @Override
    public void fullDoseSync() {
        final Long currentTime = DateUtil.currentTime();
        final BatchStatus runStatus = run(buildStep(getFullDoseItemReader(), pageSize));
        if (runStatus == BatchStatus.COMPLETED && StringUtil.isNotBlank(platformItemSyncConfig.getMallShopNo()) &&
                platformItemSyncConfig.isClearInvalidSku()) {
            platformItemSyncService.deleteInvalidPlatformItem(platformItemSyncConfig.getMallShopNo(), currentTime);
        }
    }
    
    
    @Override
    public Platform defaultType() {
        return Platform.LAOBASHOP;
    }
    
    
    public ItemReader<SkuPageListItemVO> getFullDoseItemReader() {
        pageSize = 10;
        return getItemReader(iPage -> {
            final ItemSkuPageQuery itemSkuPageQuery = ItemSkuPageQuery.of(iPage.getCurrent(), iPage.getSize());
            itemSkuPageQuery.setShopId(platformItemSyncConfig.getMallShopId());
            Result<Page<SkuPageListItemVO>> skuPage = itemFeignClient.getSkuPageList(itemSkuPageQuery);
            if (skuPage == null) {
                return null;
            }
            Page<SkuPageListItemVO> data = skuPage.getData();
            if (data == null || data.getRecords() == null || data.getRecords().isEmpty()) {
                return null;
            }
            log.debug("[平台商品同步][{}][全量同步]同步到数据，当前页码={}，每页大小={}，当前页数据={}，总数={}",
                    defaultType().getDesc(), data
                            .getCurrent(), data.getSize(), data.getRecords().size(), data.getTotal());
            return data;
        }, pageSize);
    }
    
    @Override
    public ItemProcessor<SkuPageListItemVO, PlatformSkuSyncInfo> getItemProcess() {
        return this::convertPlatformSkuSyncInfo;
    }
    
    @NonNull
    private PlatformSkuSyncInfo convertPlatformSkuSyncInfo(SkuPageListItemVO itemSkuVo) {
        PlatformSkuSyncInfo platformSkuSyncInfo = new PlatformSkuSyncInfo();
        platformSkuSyncInfo.setPlatform(defaultType());
        platformSkuSyncInfo.setShopNo(platformItemSyncConfig.getMallShopNo());
        platformSkuSyncInfo.setOuterSkuId(String.valueOf(itemSkuVo.getId()));
        platformSkuSyncInfo.setOuterItemId(String.valueOf(itemSkuVo.getItemId()));
        platformSkuSyncInfo.setStock(itemSkuVo.getStock().longValue());
        platformSkuSyncInfo.setStatus(getStatus(itemSkuVo.getShelfStatus()));
//        platformSkuSyncInfo.setStatus(Objects.equals(itemSkuVo.getShelfStatus(), 1) ? 1 : 0);
        platformSkuSyncInfo.setOuterSkuCode(itemSkuVo.getSkuNo());
        platformSkuSyncInfo.setOuterItemCode(itemSkuVo.getItemNo());
        platformSkuSyncInfo.setGoodsName(itemSkuVo.getItemName());
        platformSkuSyncInfo.setSpecName(itemSkuVo.getName());
        platformSkuSyncInfo.setSkuNum(null);
        platformSkuSyncInfo.setPrice(itemSkuVo.getSalePrice());
        return platformSkuSyncInfo;
    }
    
    /**
     * @param platformStatus 0未上架 1已上架 3已下架
     * @return status          0下架 1上架（在售中）2 待上架（未上架）
     */
    private Integer getStatus(Integer platformStatus) {
        switch (platformStatus) {
            case 0:
                return 2;
            case 1:
                return 1;
            default:
                return 0;
        }
    }
    
    @Override
    public ItemWriter<PlatformSkuSyncInfo> getItemWriter() {
        return skuInventoryList -> saveSkuSyncInfo((List<PlatformSkuSyncInfo>) skuInventoryList);
    }
    
    @Override
    protected List<PlatformSkuSyncInfo> getSkuSyncInfos(String shopNo, String itemId) {
        final ItemSkuPageQuery itemSkuPageQuery = ItemSkuPageQuery.of(1L, 100L);
        itemSkuPageQuery.setItemId(Long.valueOf(itemId));
        itemSkuPageQuery.setShopId(platformItemSyncConfig.getMallShopId());
        Result<Page<SkuPageListItemVO>> skuPage = itemFeignClient.getSkuPageList(itemSkuPageQuery);
        
        return skuPage.getData()
                .getRecords()
                .stream()
                .map(this::convertPlatformSkuSyncInfo)
                .collect(Collectors.toList());
    }
}
