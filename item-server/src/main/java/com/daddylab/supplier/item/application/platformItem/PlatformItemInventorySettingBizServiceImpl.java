package com.daddylab.supplier.item.application.platformItem;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.StopWatch;
import cn.hutool.core.map.MapBuilder;
import com.alibaba.cola.dto.SingleResponse;
import com.daddylab.supplier.item.application.platformItem.data.*;
import com.daddylab.supplier.item.common.ExceptionPlusFactory;
import com.daddylab.supplier.item.common.enums.ErrorCode;
import com.daddylab.supplier.item.domain.common.enums.Platform;
import com.daddylab.supplier.item.domain.operateLog.enums.OperateLogTarget;
import com.daddylab.supplier.item.domain.operateLog.service.OperateLogDomainService;
import com.daddylab.supplier.item.infrastructure.common.UserContext;
import com.daddylab.supplier.item.infrastructure.exceptions.BizExceptionWithData;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.*;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.PlatformItemStatus;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.*;
import com.daddylab.supplier.item.infrastructure.utils.DiffUtil;
import com.daddylab.supplier.item.infrastructure.utils.StringUtil;
import com.daddylab.supplier.item.types.platformItem.AutoInventoryRatioCmd;
import com.daddylab.supplier.item.types.platformItem.CheckInventoryRatioResult;
import lombok.extern.slf4j.Slf4j;
import org.javers.core.diff.Diff;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2024/3/15
 */
@Service
@Slf4j
public class PlatformItemInventorySettingBizServiceImpl
    implements PlatformItemInventorySettingBizService {
  @Autowired private IPlatformItemInventorySettingService platformItemInventorySettingService;
  @Autowired private IPlatformItemSkuInventorySettingService platformItemSkuInventorySettingService;
  @Autowired private OperateLogDomainService operateLogDomainService;

  @Override
  public PlatformItemInventorySettingVO getPlatformItemInventorySettingVO(
      Platform platform, String shopNo, String outerItemId) {
    final PlatformItemInventorySetting one =
        platformItemInventorySettingService.getByOuterItemId(platform, shopNo, outerItemId);
    final PlatformItemInventorySettingVO platformItemInventorySettingVO =
        new PlatformItemInventorySettingVO();
    if (one == null) {
      platformItemInventorySettingVO.setType(
          PlatformItemInventorySettingType.AUTO_ALLOCATE_PROPORTIONALLY);
      platformItemInventorySettingVO.setInventoryRatio(100);
      platformItemInventorySettingVO.setLockNum(0);
    } else {
      platformItemInventorySettingVO.setType(one.getType());
      platformItemInventorySettingVO.setInventoryRatio(one.getInventoryRatio());
      platformItemInventorySettingVO.setLockNum(one.getLockNum());
    }
    return platformItemInventorySettingVO;
  }

  @Override
  public List<PlatformItemSkuInventorySetting> getPlatformItemSkuInventorySettings(
      Platform platform, String shopNo, String outerItemId) {
    return platformItemSkuInventorySettingService
        .lambdaQuery()
        .eq(PlatformItemSkuInventorySetting::getPlatform, platform)
        .eq(PlatformItemSkuInventorySetting::getShopNo, shopNo)
        .eq(PlatformItemSkuInventorySetting::getOuterItemId, outerItemId)
        .list();
  }

  @Autowired private IPlatformItemSkuService platformItemSkuService;

  @Autowired private ICombinationItemService combinationItemService;

  @Autowired private IComposeSkuService composeSkuService;

  private CheckInventoryRatioResult checkInventoryRatio(
      String shopNo,
      String outerSkuCode,
      String currentOuterSkuId,
      Integer currentSkuInventoryRatio) {
    final CheckInventoryRatioResult checkResult = new CheckInventoryRatioResult();
    final ArrayList<CheckInventoryRatioResult.RelatedSku> relatedSkus = new ArrayList<>();
    checkResult.setPass(true);
    checkResult.setOuterSkuId(currentOuterSkuId);
    checkResult.setOuterSkuCode(outerSkuCode);
    checkResult.setTotalRatio(0);
    checkResult.setRelatedSkus(relatedSkus);

    final List<PlatformItemSkuInventorySetting> inventorySettings =
        platformItemSkuInventorySettingService
            .selectByShopNoAndSkuCode(shopNo, outerSkuCode)
            .stream()
            .filter(
                v -> v.getType() == PlatformItemInventorySettingType.AUTO_ALLOCATE_PROPORTIONALLY)
            .collect(Collectors.toList());
    final List<PlatformItemSkuInventorySetting> otherInventorySettings =
        inventorySettings.stream()
            .filter(v -> !Objects.equals(v.getOuterSkuId(), currentOuterSkuId))
            .collect(Collectors.toList());
    if (!otherInventorySettings.isEmpty()) {
      final int sumRatio =
          otherInventorySettings.stream()
              .mapToInt(PlatformItemSkuInventorySetting::getInventoryRatio)
              .sum();
      for (PlatformItemSkuInventorySetting otherInventorySetting : otherInventorySettings) {
        final CheckInventoryRatioResult.RelatedSku relatedSku =
            new CheckInventoryRatioResult.RelatedSku();
        relatedSku.setInventoryRatio(otherInventorySetting.getInventoryRatio());
        relatedSku.setOuterItemId(otherInventorySetting.getOuterItemId());
        relatedSku.setOuterSkuId(otherInventorySetting.getOuterSkuId());
        relatedSku.setOuterSkuCode(otherInventorySetting.getSkuCode());

        relatedSkus.add(relatedSku);
      }
      final int totalRatio = sumRatio + currentSkuInventoryRatio;
      checkResult.setTotalRatio(totalRatio);
      if (totalRatio > 100) {
        checkResult.setPass(false);
      }
    }

    final Long combinationItemId = combinationItemService.isCombinationItem(outerSkuCode);
    if (combinationItemId != null && combinationItemId > 0) {
      final ArrayList<CheckInventoryRatioResult.ComposeSku> composeSkuResults = new ArrayList<>();
      final List<ComposeSku> composeSkus =
          composeSkuService.selectByCombinationId(combinationItemId);
      for (ComposeSku composeSku : composeSkus) {
        final CheckInventoryRatioResult checkInventoryRatioResultForComposeSku =
            checkInventoryRatio(
                shopNo, composeSku.getSkuCode(), currentOuterSkuId, currentSkuInventoryRatio);
        final List<CheckInventoryRatioResult.RelatedSku> relatedSkusForComposeSku =
            checkInventoryRatioResultForComposeSku.getRelatedSkus();
        final CheckInventoryRatioResult.ComposeSku composeSkuResult =
            new CheckInventoryRatioResult.ComposeSku();
        composeSkuResult.setSkuCode(composeSku.getSkuCode());
        final int sumRatio =
            relatedSkusForComposeSku.stream()
                .mapToInt(CheckInventoryRatioResult.RelatedSku::getInventoryRatio)
                .sum();
        composeSkuResult.setTotalRatio(sumRatio + currentSkuInventoryRatio);
        composeSkuResult.setRelatedSkus(relatedSkusForComposeSku);
        composeSkuResult.setPass(checkInventoryRatioResultForComposeSku.isPass());
        composeSkuResults.add(composeSkuResult);
      }
      checkResult.setComposeSkus(composeSkuResults);
    }
    return checkResult;
  }

  @Override
  @Transactional
  public void syncInventorySettingViewObject(PlatformItemDetail viewObject) {
    final Platform platform = viewObject.getPlatform();
    final String outerItemId = viewObject.getOuterItemId();
    final String shopNo = viewObject.getShopNo();
    //        final PlatformItemInventorySettingVO inventorySettingVO =
    // viewObject.getInventorySetting();
    //        final PlatformItemInventorySettingType type = inventorySettingVO.getType();
    PlatformItemInventorySetting settingPO =
        platformItemInventorySettingService.getByOuterItemId(platform, shopNo, outerItemId);
    if (settingPO == null) {
      settingPO = new PlatformItemInventorySetting();
      settingPO.setOuterItemId(outerItemId);
      settingPO.setPlatform(platform);
      settingPO.setShopNo(shopNo);
      //            settingPO.setType(inventorySettingVO.getType());
      //            settingPO.setInventoryRatio(inventorySettingVO.getInventoryRatio());
      //            settingPO.setLockNum(inventorySettingVO.getLockNum());
      final StringJoiner logBuilder = new StringJoiner("，");
      //            logBuilder.add(String.format("库存设置修改为“%s”", type.getDesc()));
      //            if (type == PlatformItemInventorySettingType.AUTO_ALLOCATE_PROPORTIONALLY) {
      //                logBuilder.add(String.format("库存占比修改为“%s”", settingPO.getInventoryRatio()));
      //            } else {
      //                logBuilder.add(String.format("锁定库存修改为“%s”", settingPO.getLockNum()));
      //            }
      operateLogDomainService.addOperatorLog(
          UserContext.getUserId(),
          OperateLogTarget.PLATFORM_ITEM,
          PlatformItemOpLogId.of(platform, outerItemId),
          logBuilder.toString(),
          MapBuilder.create().put("new", settingPO).build());
    } else {
      PlatformItemInventorySetting original = Assembler.INST.copy(settingPO);
      settingPO.setPlatform(platform);
      //            settingPO.setType(inventorySettingVO.getType());
      //            if (type == PlatformItemInventorySettingType.AUTO_ALLOCATE_PROPORTIONALLY) {
      //                settingPO.setInventoryRatio(inventorySettingVO.getInventoryRatio());
      //            } else {
      //                settingPO.setLockNum(inventorySettingVO.getLockNum());
      //            }
      if (original.getType() != settingPO.getType()) {
        final StringJoiner logBuilder = new StringJoiner("，");
        logBuilder.add(
            String.format(
                "库存设置从“%s”修改为“%s”", original.getType().getDesc(), settingPO.getType().getDesc()));
        //                if (type == PlatformItemInventorySettingType.AUTO_ALLOCATE_PROPORTIONALLY)
        // {
        //                    logBuilder.add(String.format("库存占比修改为“%s”",
        // settingPO.getInventoryRatio()));
        //                } else {
        //                    logBuilder.add(String.format("锁定库存修改为“%s”", settingPO.getLockNum()));
        //                }
        operateLogDomainService.addOperatorLog(
            UserContext.getUserId(),
            OperateLogTarget.PLATFORM_ITEM,
            PlatformItemOpLogId.of(platform, outerItemId),
            logBuilder.toString(),
            MapBuilder.create().put("original", original).put("new", settingPO).build());
      }
    }
    platformItemInventorySettingService.saveOrUpdate(settingPO);

    final List<PlatformItemSkuInventorySetting> platformItemSkuInventorySettings =
        getPlatformItemSkuInventorySettings(platform, shopNo, outerItemId);
    for (PlatformItemDetailSku platformItemDetailSku : viewObject.getSkus()) {
      final CheckInventoryRatioResult checkInventoryRatioResult =
          checkInventoryRatio(
              shopNo,
              platformItemDetailSku.getOuterSkuCode(),
              platformItemDetailSku.getOuterSkuId(),
              0
              //
              //        platformItemDetailSku.getInventoryRatio()
              );
      if (!checkInventoryRatioResult.isPass() || !checkInventoryRatioResult.allComposeSkuPass()) {
        final StringJoiner msg = new StringJoiner("，");
        if (!checkInventoryRatioResult.isPass()) {
          msg.add(
              String.format(
                  "编码“%s”平台商品规格累计占比之和超过100%%（%s），",
                  checkInventoryRatioResult.getOuterSkuCode(),
                  checkInventoryRatioResult.getTotalRatio()));
        }
        final List<CheckInventoryRatioResult.ComposeSku> composeSkuNotPassResults =
            checkInventoryRatioResult.getComposeSkus().stream()
                .filter(v -> !v.isPass())
                .collect(Collectors.toList());
        if (!composeSkuNotPassResults.isEmpty()) {
          for (CheckInventoryRatioResult.ComposeSku composeSkuNotPassResult :
              composeSkuNotPassResults) {
            msg.add(
                String.format(
                    "组合装“%s”套内单品“%s”累计占比之和超过100%%（%s）",
                    checkInventoryRatioResult.getOuterSkuCode(),
                    composeSkuNotPassResult.getSkuCode(),
                    composeSkuNotPassResult.getTotalRatio()));
          }
        }
        final BizExceptionWithData bizExceptionWithData =
            new BizExceptionWithData(ErrorCode.PLATFORM_ITEM_ERROR, msg.toString());
        bizExceptionWithData.setData(checkInventoryRatioResult);
        throw bizExceptionWithData;
      }

      //            if (type == PlatformItemInventorySettingType.LOCK_INVENTORY) {
      //                if
      // (platformItemDetailSku.getSyncStock().compareTo(platformItemDetailSku.getAvailableStock())
      // > 0) {
      //                    throw ExceptionPlusFactory.bizException(ErrorCode.PLATFORM_ITEM_ERROR,
      //                                                            "同步库存设置不应该大于可用库存");
      //                }
      //            }
      boolean isUpdate = false;
      for (PlatformItemSkuInventorySetting platformItemSkuInventorySetting :
          platformItemSkuInventorySettings) {
        //                platformItemSkuInventorySetting.setType(type);
        if (StringUtil.isBlank(platformItemSkuInventorySetting.getCombinationCode())
            && platformItemSkuInventorySetting
                .getOuterSkuId()
                .equals(platformItemDetailSku.getOuterSkuId())
            && platformItemSkuInventorySetting
                .getSkuCode()
                .equals(platformItemDetailSku.getOuterSkuCode())) {

          PlatformItemSkuInventorySetting original =
              Assembler.INST.copy(platformItemSkuInventorySetting);
          platformItemSkuInventorySetting.setOuterItemId(outerItemId);
          platformItemSkuInventorySetting.setOuterSkuId(platformItemDetailSku.getOuterSkuId());
          platformItemSkuInventorySetting.setSkuCode(platformItemDetailSku.getOuterSkuCode());
          //                    if (type ==
          // PlatformItemInventorySettingType.AUTO_ALLOCATE_PROPORTIONALLY) {
          //
          // platformItemSkuInventorySetting.setInventoryRatio(platformItemDetailSku.getInventoryRatio());
          //                    } else {
          //
          // platformItemSkuInventorySetting.setLockNum(platformItemDetailSku.getSyncStock());
          //                    }
          //
          // platformItemSkuInventorySetting.setSafetyThreshold(platformItemDetailSku.getSafeThreshold());
          final Diff diff = DiffUtil.diff(original, platformItemSkuInventorySetting);
          platformItemSkuInventorySettingService.updateById(platformItemSkuInventorySetting);
          final StringJoiner logBuilder = new StringJoiner("，");
          //                    if (type ==
          // PlatformItemInventorySettingType.AUTO_ALLOCATE_PROPORTIONALLY) {
          //                        if (!Objects.equals(original.getInventoryRatio(),
          //
          // platformItemSkuInventorySetting.getInventoryRatio())) {
          //                            logBuilder.add(String.format("库存占比从“%s”修改为“%s”",
          //                                                         original.getInventoryRatio(),
          //
          // platformItemSkuInventorySetting.getInventoryRatio()));
          //                        }
          //                    }
          //                    else {
          //                        if (!Objects.equals(original.getLockNum(),
          // platformItemSkuInventorySetting.getLockNum())) {
          //                            logBuilder.add(String.format("同步库存从“%s”修改为“%s”",
          //                                                         original.getLockNum(),
          //
          // platformItemSkuInventorySetting.getLockNum()));
          //                        }
          //                    }
          // 如果原安全库存值为-1，说明将从店铺设置的安全库存同步到SKU，不需要记录操作日志（理论上可能不会有这个场景，预防一下）
          if (original.getSafetyThreshold() != -1
              && !Objects.equals(
                  original.getSafetyThreshold(),
                  platformItemSkuInventorySetting.getSafetyThreshold())) {
            logBuilder.add(
                String.format(
                    "安全库存从“%s”修改为“%s”",
                    original.getSafetyThreshold(),
                    platformItemSkuInventorySetting.getSafetyThreshold()));
          }
          if (logBuilder.length() > 0) {
            operateLogDomainService.addOperatorLog(
                UserContext.getUserId(),
                OperateLogTarget.PLATFORM_ITEM,
                PlatformItemOpLogId.of(platform, outerItemId),
                String.format(
                    "将 %s-%s 的 %s",
                    platformItemDetailSku.getOuterSkuId(),
                    platformItemDetailSku.getOuterSkuCode(),
                    logBuilder),
                diff);
          }
          isUpdate = true;
          break;
        }
      }
      if (!isUpdate) {
        final PlatformItemSkuInventorySetting platformItemSkuInventorySetting =
            new PlatformItemSkuInventorySetting();
        platformItemSkuInventorySetting.setPlatform(platform);
        platformItemSkuInventorySetting.setShopNo(shopNo);
        platformItemSkuInventorySetting.setOuterItemId(outerItemId);
        platformItemSkuInventorySetting.setOuterSkuId(platformItemDetailSku.getOuterSkuId());
        //                if (type == PlatformItemInventorySettingType.AUTO_ALLOCATE_PROPORTIONALLY)
        // {
        //
        // platformItemSkuInventorySetting.setInventoryRatio(platformItemDetailSku.getInventoryRatio());
        //                    platformItemSkuInventorySetting.setLockNum(0);
        //                } else {
        //                    platformItemSkuInventorySetting.setInventoryRatio(0);
        //
        // platformItemSkuInventorySetting.setLockNum(platformItemDetailSku.getSyncStock());
        //                }
        platformItemSkuInventorySetting.setSkuCode(platformItemDetailSku.getOuterSkuCode());
        //
        // platformItemSkuInventorySetting.setSafetyThreshold(platformItemDetailSku.getSafeThreshold());
        platformItemSkuInventorySettingService.save(platformItemSkuInventorySetting);

        final StringBuilder logBuilder = new StringBuilder();
        //                if (type == PlatformItemInventorySettingType.AUTO_ALLOCATE_PROPORTIONALLY)
        // {
        //                    logBuilder.append("库存占比修改为“")
        //                              .append(platformItemSkuInventorySetting.getInventoryRatio())
        //                              .append("“");
        //                } else {
        //                    logBuilder.append("同步库存修改为”")
        //                              .append(platformItemSkuInventorySetting.getLockNum())
        //                              .append("“");
        //                }
        logBuilder
            .append("，安全库存修改为“")
            .append(platformItemSkuInventorySetting.getSafetyThreshold())
            .append("“");
        operateLogDomainService.addOperatorLog(
            UserContext.getUserId(),
            OperateLogTarget.PLATFORM_ITEM,
            PlatformItemOpLogId.of(platform, outerItemId),
            String.format(
                "将 %s-%s 的 %s",
                platformItemDetailSku.getOuterSkuId(),
                platformItemDetailSku.getOuterSkuCode(),
                logBuilder),
            platformItemSkuInventorySetting);
      }

      //            if (CollectionUtil.isNotEmpty(platformItemDetailSku.getComposeSkus())) {
      //                final ArrayList<PlatformItemSkuInventorySetting> composeSkuSettings = new
      // ArrayList<>();
      //                for (PlatformItemDetailSkuComposeItem composeSku :
      // platformItemDetailSku.getComposeSkus()) {
      //                    final PlatformItemSkuInventorySetting platformItemSkuInventorySetting =
      // new PlatformItemSkuInventorySetting();
      //                    platformItemSkuInventorySetting.setPlatform(platform);
      //                    platformItemSkuInventorySetting.setShopNo(shopNo);
      //
      // platformItemSkuInventorySetting.setOuterItemId(viewObject.getOuterItemId());
      //
      // platformItemSkuInventorySetting.setOuterSkuId(platformItemDetailSku.getOuterSkuId());
      //
      // platformItemSkuInventorySetting.setInventoryRatio(platformItemDetailSku.getInventoryRatio());
      //                    platformItemSkuInventorySetting.setType(type);
      //                    platformItemSkuInventorySetting.setSkuCode(composeSku.getSkuCode());
      //
      // platformItemSkuInventorySetting.setCombinationCode(platformItemDetailSku.getOuterSkuCode());
      //                    composeSkuSettings.add(platformItemSkuInventorySetting);
      //                }
      //                final HashSet<PlatformItemSkuInventorySetting> newSettingSet = new
      // HashSet<>(
      //                        composeSkuSettings);
      //                final List<PlatformItemSkuInventorySetting> toRemoveSettings =
      // platformItemSkuInventorySettings
      //                        .stream()
      //                        .filter(v ->
      // v.getOuterSkuId().equals(platformItemDetailSku.getOuterSkuId()))
      //                        .filter(v -> StringUtil.isNotBlank(v.getCombinationCode()))
      //                        .filter(v -> !newSettingSet.contains(v))
      //                        .collect(Collectors.toList());
      //                if (!toRemoveSettings.isEmpty()) {
      //                    platformItemSkuInventorySettingService.removeByIdsWithTime(
      //                            toRemoveSettings.stream()
      //                                            .map(PlatformItemSkuInventorySetting::getId)
      //                                            .collect(Collectors.toList()));
      //                }
      //
      // platformItemSkuInventorySettingService.saveOrUpdateBatchByOuterId(composeSkuSettings);
      //            }
    }
  }

  @Override
  public SingleResponse<Integer> autoInventoryRatio(AutoInventoryRatioCmd cmd) {
    final String shopNo = cmd.getShopNo();
    final List<String> skuCodes = cmd.getSkuCodes();
    if (CollectionUtil.isEmpty(skuCodes)) {
      throw ExceptionPlusFactory.bizException(ErrorCode.VERIFY_PARAM, "店铺编号,SKU编码 不能为空");
    }
    log.debug("[平台商品库存设置][自动分配库存占比]开始执行 cmd={}", cmd);
    final StopWatch stopWatch = new StopWatch();
    stopWatch.start();

    final HashSet<String> allSkuCodes = new LinkedHashSet<>(skuCodes);

    // 查询SKU编码对应的组合装
    final List<CombinationItem> combinationItems =
        combinationItemService.lambdaQuery().in(CombinationItem::getCode, skuCodes).list();
    final Set<Long> combinationIds =
        combinationItems.stream().map(CombinationItem::getId).collect(Collectors.toSet());
    final Set<String> combinationCodes =
        combinationItems.stream().map(CombinationItem::getCode).collect(Collectors.toSet());

    // 查询组合装套内单品
    final List<ComposeSku> composeSkus =
        combinationIds.isEmpty()
            ? Collections.emptyList()
            : composeSkuService
                .lambdaQuery()
                .in(ComposeSku::getCombinationId, combinationIds)
                .list();

    final Set<String> composeSkuCodes =
        composeSkus.stream().map(ComposeSku::getSkuCode).collect(Collectors.toSet());
    allSkuCodes.addAll(composeSkuCodes);

    // 查询包含这些[组合装SKU编码]的其他组合装
    final List<ComposeSku> composeSkus1 =
        composeSkuCodes.isEmpty()
            ? Collections.emptyList()
            : composeSkuService.lambdaQuery().in(ComposeSku::getSkuCode, composeSkuCodes).list();
    final Set<Long> combinationIds1 =
        composeSkus1.stream()
            .map(ComposeSku::getCombinationId)
            .filter(id -> !combinationIds.contains(id))
            .collect(Collectors.toSet());
    final List<CombinationItem> combinationItems1 =
        combinationIds1.isEmpty()
            ? Collections.emptyList()
            : combinationItemService.listByIds(combinationIds1);
    final Set<String> combinationCodes1 =
        combinationItems1.stream().map(CombinationItem::getCode).collect(Collectors.toSet());
    allSkuCodes.addAll(combinationCodes1);

    final HashSet<CombinationItem> allCombinationItems = new HashSet<>(combinationItems);
    allCombinationItems.addAll(combinationItems1);

    final Map<Long, CombinationItem> allCombinationItemMapById =
        allCombinationItems.stream()
            .collect(Collectors.toMap(CombinationItem::getId, Function.identity()));

    final Map<String, CombinationItem> allCombinationItemMapByCode =
        allCombinationItems.stream()
            .collect(Collectors.toMap(CombinationItem::getCode, Function.identity()));

    final HashSet<ComposeSku> allComposeSkus = new HashSet<>(composeSkus);
    allComposeSkus.addAll(composeSkus1);

    final Map<String, Map<Long, List<ComposeSku>>> allComposeSkusGroupBySkuCodeAndCombinationId =
        allComposeSkus.stream()
            .collect(
                Collectors.groupingBy(
                    ComposeSku::getSkuCode, Collectors.groupingBy(ComposeSku::getCombinationId)));

    final Map<Long, List<ComposeSku>> allComposeSkusGroupByCombinationId =
        allComposeSkus.stream().collect(Collectors.groupingBy(ComposeSku::getCombinationId));

    final HashSet<String> allCombinationCodes = new LinkedHashSet<>();
    allCombinationCodes.add("pseudo");
    allCombinationCodes.addAll(combinationCodes);
    allCombinationCodes.addAll(combinationCodes1);

    final List<PlatformItemSkuInventorySetting> localSettings =
        platformItemSkuInventorySettingService
            .lambdaQuery()
            .eq(PlatformItemSkuInventorySetting::getShopNo, shopNo)
            .and(
                q ->
                    q.in(PlatformItemSkuInventorySetting::getSkuCode, skuCodes)
                        .or()
                        .in(
                            PlatformItemSkuInventorySetting::getCombinationCode,
                            allCombinationCodes))
            .list();

    // 查询以上编码相关的所有平台商品SKU[在售]
    final List<PlatformItemSku> allPlatformItemSkus =
        platformItemSkuService
            .lambdaQuery()
            .eq(PlatformItemSku::getShopNo, shopNo)
            .in(PlatformItemSku::getOuterSkuCode, allSkuCodes)
            .eq(PlatformItemSku::getStatus, PlatformItemStatus.ON_SALE)
            .list();

    final Map<String, List<PlatformItemSku>> allPlatformItemSkusGroupByOuterSkuCode =
        allPlatformItemSkus.stream()
            .collect(Collectors.groupingBy(PlatformItemSku::getOuterSkuCode));

    final Function<String, List<CombinationItem>> composeSkuCodeToCombinationItemFunction =
        skuCode ->
            allComposeSkusGroupBySkuCodeAndCombinationId
                .getOrDefault(skuCode, Collections.emptyMap())
                .keySet()
                .stream()
                .filter(allCombinationItemMapById::containsKey)
                .map(allCombinationItemMapById::get)
                .collect(Collectors.toList());

    final Set<PlatformItemSkuInventorySetting> models = new LinkedHashSet<>();
    for (String skuCode : allSkuCodes) {
      // 先查询是否编码直接匹配的平台商品SKU
      final Set<PlatformItemSku> platformItemSkus =
          new LinkedHashSet<>(
              allPlatformItemSkusGroupByOuterSkuCode.getOrDefault(
                  skuCode, Collections.emptyList()));

      // 如果该编码为组合装编码
      final CombinationItem combinationItem = allCombinationItemMapByCode.get(skuCode);
      if (combinationItem != null) {
        final List<ComposeSku> theComposeSkus =
            allComposeSkusGroupByCombinationId.getOrDefault(
                combinationItem.getId(), Collections.emptyList());
        for (ComposeSku cs : theComposeSkus) {
          final String csSkuCode = cs.getSkuCode();
          platformItemSkus.addAll(
              allPlatformItemSkusGroupByOuterSkuCode.getOrDefault(
                  csSkuCode, Collections.emptyList()));

          // 检查是否存在包含此单品的其他组合装
          composeSkuCodeToCombinationItemFunction.apply(csSkuCode).stream()
              .filter(v -> !Objects.equals(cs.getCombinationId(), v.getId()))
              .map(CombinationItem::getCode)
              .map(allPlatformItemSkusGroupByOuterSkuCode::get)
              .filter(Objects::nonNull)
              .forEach(platformItemSkus::addAll);
        }
      } else {
        // 如果该编码为普通单品编码，需检查是否存在包含此单品的组合装
        composeSkuCodeToCombinationItemFunction.apply(skuCode).stream()
            .map(CombinationItem::getCode)
            .map(allPlatformItemSkusGroupByOuterSkuCode::get)
            .filter(Objects::nonNull)
            .forEach(platformItemSkus::addAll);
      }
      if (!platformItemSkus.isEmpty()) {
        final int skuNum = platformItemSkus.size();
        final int avgRatio = 100 / skuNum;
        final int remainRatio = 100 % skuNum;
        int i = 0;
        for (PlatformItemSku item : platformItemSkus) {
          i++;
          final PlatformItemSkuInventorySetting model = new PlatformItemSkuInventorySetting();
          model.setPlatform(item.getPlatform());
          model.setShopNo(item.getShopNo());
          model.setOuterItemId(item.getOuterItemId());
          model.setOuterSkuId(item.getOuterSkuId());
          if (i == 1) {
            model.setInventoryRatio(avgRatio + remainRatio);
          } else {
            model.setInventoryRatio(avgRatio);
          }
          final String outerSkuCode = item.getOuterSkuCode();
          model.setSkuCode(outerSkuCode);
          models.add(model);
          Optional.ofNullable(allCombinationItemMapByCode.get(outerSkuCode))
              .map(CombinationItem::getId)
              .map(allComposeSkusGroupByCombinationId::get)
              .ifPresent(
                  composeSkuList -> {
                    for (ComposeSku composeSku : composeSkuList) {
                      final PlatformItemSkuInventorySetting model1 =
                          PlatformItemSkuInventorySettingAssembler.INSTANCE.copy(model);
                      model1.setSkuCode(composeSku.getSkuCode());
                      model1.setCombinationCode(outerSkuCode);
                      models.add(model1);
                    }
                  });
        }
      }
    }

    stopWatch.stop();
    int affectRow = 0;
    if (!models.isEmpty()) {
      for (PlatformItemSkuInventorySetting model : models) {
        final ArrayList<String> code = new ArrayList<>();
        if (StringUtil.isNotBlank(model.getCombinationCode())) {
          code.add(model.getCombinationCode());
        }
        code.add(model.getSkuCode());
        log.debug(
            "[平台商品库存设置][自动分配库存占比]S:{} OID:{} OSID:{} C:{} IR:{}",
            model.getShopNo(),
            model.getOuterItemId(),
            model.getOuterSkuId(),
            code,
            model.getInventoryRatio());
      }
      if (cmd.isDebug()) {
        affectRow = models.size();
      } else {
        affectRow =
            platformItemSkuInventorySettingService.saveOrUpdateBatchByOuterId(
                new ArrayList<>(models));
      }
      if (!localSettings.isEmpty()) {
        final Set<PlatformItemSkuInventorySetting> toRemoveSettings =
            localSettings.stream().filter(v -> !models.contains(v)).collect(Collectors.toSet());
        if (!toRemoveSettings.isEmpty()) {
          final List<Long> ids =
              toRemoveSettings.stream()
                  .map(PlatformItemSkuInventorySetting::getId)
                  .collect(Collectors.toList());
          platformItemSkuInventorySettingService.removeByIdsWithTime(ids);
          log.info("[平台商品库存设置][自动分配库存占比]删除 {} 条已失效配置 {} cmd={}", ids.size(), ids, cmd);
        }
      }
      log.info(
          "[平台商品库存设置][自动分配库存占比]完成 cmd={} affectRow={} 耗时={}ms",
          cmd,
          affectRow,
          stopWatch.getTotalTimeMillis());
    } else {
      log.info("[平台商品库存设置][自动分配库存占比]未找到任何符合条件的平台商品SKU cmd={}", cmd);
      if (!localSettings.isEmpty()) {
        final List<Long> ids =
            localSettings.stream()
                .map(PlatformItemSkuInventorySetting::getId)
                .collect(Collectors.toList());
        platformItemSkuInventorySettingService.removeByIdsWithTime(ids);
        log.info("[平台商品库存设置][自动分配库存占比]删除 {} 条已失效配置 {} cmd={}", ids.size(), ids, cmd);
      }
    }
    return SingleResponse.of(affectRow);
  }

  public static void main(String[] args) {
    int n = 3;
    final int avgRatio = (int) Math.floor(1.0 * 100 / n);
    System.out.println(avgRatio);
  }

  @Mapper
  interface Assembler {
    Assembler INST = Mappers.getMapper(Assembler.class);

    PlatformItemSkuInventorySetting copy(
        PlatformItemSkuInventorySetting platformItemSkuInventorySetting);

    PlatformItemInventorySetting copy(PlatformItemInventorySetting platformItemInventorySetting);
  }
}
