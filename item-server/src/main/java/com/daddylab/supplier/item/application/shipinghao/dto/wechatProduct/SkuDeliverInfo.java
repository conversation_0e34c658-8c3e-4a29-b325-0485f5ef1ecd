package com.daddylab.supplier.item.application.shipinghao.dto.wechatProduct;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data; /** SKU发货信息 */
@Data
public class SkuDeliverInfo {
  /** 库存类型：0-现货，1-预约 */
  @JsonProperty("stock_type")
  private Integer stockType;

  /** 全款预售发货类型：0-付定金时间，1-付尾款时间 */
  @JsonProperty("full_payment_presale_delivery_type")
  private Integer fullPaymentPresaleDeliveryType;

  /** 预售开始时间戳 */
  @JsonProperty("presale_begin_time")
  private Long presaleBeginTime;

  /** 预售结束时间戳 */
  @JsonProperty("presale_end_time")
  private Long presaleEndTime;

  /** 全款预售发货时间戳 */
  @JsonProperty("full_payment_presale_delivery_time")
  private Long fullPaymentPresaleDeliveryTime;
}
