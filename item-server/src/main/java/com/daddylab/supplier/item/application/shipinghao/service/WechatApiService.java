package com.daddylab.supplier.item.application.shipinghao.service;

import com.daddylab.supplier.item.application.shipinghao.dto.WechatAccessTokenDto;
import com.daddylab.supplier.item.application.shipinghao.dto.WechatProductDetailDto;
import com.daddylab.supplier.item.application.shipinghao.dto.WechatProductListDto;

/**
 * 微信API服务接口
 *
 * <AUTHOR>
 * @since 2024-12-19
 */
public interface WechatApiService {

    /**
     * 获取访问令牌
     */
    WechatAccessTokenDto getAccessToken(String type);


    /**
     * 获取商品列表（正确的next_key分页方式）
     * 
     * @param type 微信配置类型 daddylab/member
     * @param accessToken 访问令牌
     * @param pageSize 每页数量（不超过30）
     * @param nextKey 分页游标，null表示获取第一页
     * @return 商品列表
     */
    WechatProductListDto getProductListWithNextKey(String type, String accessToken, Integer pageSize, String nextKey);

    /**
     * 获取商品详情
     */
    WechatProductDetailDto getProductDetail(String type, String accessToken, String productId);

    /**
     * 清除缓存的访问令牌
     */
    void clearCachedAccessToken(String type);
} 