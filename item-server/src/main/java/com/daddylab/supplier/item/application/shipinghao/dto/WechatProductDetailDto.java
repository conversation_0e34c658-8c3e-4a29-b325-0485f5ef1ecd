package com.daddylab.supplier.item.application.shipinghao.dto;

import com.daddylab.supplier.item.application.shipinghao.dto.wechatProduct.WeChatProduct;
import lombok.Data;

/**
 * 微信商品详情DTO
 *
 * <AUTHOR>
 * @date 2024-12-19
 */
@Data
public class WechatProductDetailDto {

  /** 错误码，0表示成功 */
  private Integer errcode;

  /** 错误信息，成功时为"ok" */
  private String errmsg;

  /** 商品信息 */
  private WeChatProduct product;

  /** 判断是否成功 */
  public boolean isSuccess() {
    return errcode == null || errcode == 0;
  }
}
