package com.daddylab.supplier.item.application.shipinghao.service.impl;

import com.daddylab.supplier.item.application.shipinghao.converter.WechatProductDetailConverter;
import com.daddylab.supplier.item.application.shipinghao.dto.WechatAccessTokenDto;
import com.daddylab.supplier.item.application.shipinghao.dto.WechatProductDetailDto;
import com.daddylab.supplier.item.application.shipinghao.dto.WechatProductListDto;
import com.daddylab.supplier.item.application.shipinghao.service.WechatApiService;
import com.daddylab.supplier.item.application.shipinghao.service.WechatProductSyncService;
import com.daddylab.supplier.item.domain.wechatProductDetail.gateway.WechatProductDetailGateway;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.WechatProductDetail;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;

/**
 * 微信商品同步服务实现类
 *
 * <AUTHOR>
 * @date 2024-12-19
 */
@Slf4j
@Service
public class WechatProductSyncServiceImpl implements WechatProductSyncService {

  @Autowired private WechatApiService wechatApiService;

  @Autowired private WechatProductDetailGateway wechatProductDetailGateway;

  private final ExecutorService executorService = Executors.newFixedThreadPool(5);

  @Override
  public boolean syncAllProducts(String type) {
    try {
      log.info("开始全量同步微信商品，类型: {}", type);

      // 获取访问令牌
      WechatAccessTokenDto accessToken = wechatApiService.getAccessToken(type);
      if (accessToken == null || !accessToken.isSuccess()) {
        log.error("获取访问令牌失败，类型: {}", type);
        return false;
      }

      String token = accessToken.getAccessToken();
      log.info("获取访问令牌成功，类型: {}, 令牌: {}", type, token);

      // 分批获取商品列表，每页最多30个
      String nextKey = null;
      int pageSize = 30; // 使用最大允许的page_size
      int totalSynced = 0;

      while (true) {
        WechatProductListDto productList =
            wechatApiService.getProductListWithNextKey(type, token, pageSize, nextKey);
        if (productList == null
            || productList.getProductIds() == null
            || productList.getProductIds().isEmpty()) {
          log.info("没有更多商品数据，同步完成，类型: {}", type);
          break;
        }

        List<String> productIds = productList.getProductIds();
        log.info("获取到商品ID列表，类型: {}, 数量: {}", type, productIds.size());

        // 批量获取商品详情
        List<WechatProductDetailDto> productDetails = getProductDetails(type, productIds);

        // 处理商品详情并落库
        for (WechatProductDetailDto productDetail : productDetails) {
          if (productDetail != null) {
            log.info("获取商品详情成功，类型: {}, 标题: {}", type, productDetail.getProduct().getTitle());

            // 转换为数据库实体
            WechatProductDetail entity =
                WechatProductDetailConverter.dtoToEntity(productDetail, type);
            if (entity != null) {
              // 检查是否已存在
              WechatProductDetail existingEntity =
                  wechatProductDetailGateway.findByTypeAndProductId(type, entity.getProductId());
              if (existingEntity != null) {
                // 更新现有记录
                entity.setId(existingEntity.getId());
                entity.setCreatedAt(existingEntity.getCreatedAt());
                entity.setCreatedUid(existingEntity.getCreatedUid());
                boolean updated = wechatProductDetailGateway.updateByTypeAndProductId(entity);
                if (updated) {
                  log.info("更新商品详情成功，类型: {}, 标题: {}", type, productDetail.getProduct().getTitle());
                } else {
                  log.error("更新商品详情失败，类型: {}, 标题: {}", type, productDetail.getProduct().getTitle());
                }
              } else {
                // 新增记录
                boolean saved = wechatProductDetailGateway.save(entity);
                if (saved) {
                  log.info("保存商品详情成功，类型: {}, 标题: {}", type, productDetail.getProduct().getTitle());
                } else {
                  log.error("保存商品详情失败，类型: {}, 标题: {}", type, productDetail.getProduct().getTitle());
                }
              }
            }
          }
        }

        totalSynced += productDetails.size();
        log.info("已同步商品数量，类型: {}, 数量: {}", type, totalSynced);

        // 获取下一页的next_key
        nextKey = productList.getNextKey();
        
        // 如果没有next_key，说明已经获取完所有商品
        if (nextKey == null || nextKey.isEmpty()) {
          break;
        }
      }

      log.info("全量同步微信商品完成，类型: {}, 总共同步商品数量: {}", type, totalSynced);
      return true;

    } catch (Exception e) {
      log.error("全量同步微信商品异常，类型: {}", type, e);
      return false;
    }
  }

  @Override
  public boolean syncIncrementalProducts(String type, Long lastUpdateTime) {
    try {
      log.info("开始增量同步微信商品，类型: {}, 上次更新时间: {}", type, lastUpdateTime);

      // 如果没有提供上次更新时间，默认同步最近24小时的数据
      if (lastUpdateTime == null) {
        lastUpdateTime = System.currentTimeMillis() - 24 * 60 * 60 * 1000L; // 24小时前
        log.info("未提供上次更新时间，默认同步最近24小时的数据，lastUpdateTime: {}", lastUpdateTime);
      }

      // 获取访问令牌
      WechatAccessTokenDto accessToken = wechatApiService.getAccessToken(type);
      if (accessToken == null || !accessToken.isSuccess()) {
        log.error("获取访问令牌失败，类型: {}", type);
        return false;
      }

      String token = accessToken.getAccessToken();

      // 分批获取商品列表，每页最多30个
      String nextKey = null;
      int pageSize = 30;
      int totalChecked = 0;
      int totalSynced = 0;

      while (true) {
        WechatProductListDto productList =
            wechatApiService.getProductListWithNextKey(type, token, pageSize, nextKey);
        if (productList == null
            || productList.getProductIds() == null
            || productList.getProductIds().isEmpty()) {
          log.info("没有更多商品数据，增量同步完成，类型: {}", type);
          break;
        }

        List<String> productIds = productList.getProductIds();
        totalChecked += productIds.size();

        // 过滤出需要同步的商品ID（在数据库中不存在或更新时间晚于lastUpdateTime的）
        List<String> needSyncProductIds = new ArrayList<>();
        for (String productId : productIds) {
          WechatProductDetail existingEntity = wechatProductDetailGateway.findByTypeAndProductId(type, productId);
          
          if (existingEntity == null) {
            // 数据库中不存在，需要同步
            needSyncProductIds.add(productId);
          } else if (existingEntity.getUpdatedAt() != null && 
                     existingEntity.getUpdatedAt() < lastUpdateTime) {
            // 数据库中的记录更新时间早于指定时间，需要同步
            needSyncProductIds.add(productId);
          }
        }

        log.info("检查商品ID列表，类型: {}, 总数: {}, 需要同步: {}", type, productIds.size(), needSyncProductIds.size());

        // 如果有需要同步的商品，则获取详情并处理
        if (!needSyncProductIds.isEmpty()) {
          // 批量获取商品详情
          List<WechatProductDetailDto> productDetails = getProductDetails(type, needSyncProductIds);

          // 处理商品详情并落库
          for (WechatProductDetailDto productDetail : productDetails) {
            if (productDetail != null) {
              // 转换为数据库实体
              WechatProductDetail entity =
                  WechatProductDetailConverter.dtoToEntity(productDetail, type);
              if (entity != null) {
                // 检查是否已存在
                WechatProductDetail existingEntity =
                    wechatProductDetailGateway.findByTypeAndProductId(type, entity.getProductId());
                if (existingEntity != null) {
                  // 更新现有记录
                  entity.setId(existingEntity.getId());
                  entity.setCreatedAt(existingEntity.getCreatedAt());
                  entity.setCreatedUid(existingEntity.getCreatedUid());
                  boolean updated = wechatProductDetailGateway.updateByTypeAndProductId(entity);
                  if (!updated) {
                    log.error("更新商品详情失败，类型: {}, 标题: {}", type, productDetail.getProduct().getTitle());
                  } else {
                    totalSynced++;
                  }
                } else {
                  // 新增记录
                  boolean saved = wechatProductDetailGateway.save(entity);
                  if (!saved) {
                    log.error("保存商品详情失败，类型: {}, 标题: {}", type, productDetail.getProduct().getTitle());
                  } else {
                    totalSynced++;
                  }
                }
              }
            }
          }
        }

        // 获取下一页的next_key
        nextKey = productList.getNextKey();
        
        // 如果没有next_key，说明已经获取完所有商品
        if (nextKey == null || nextKey.isEmpty()) {
          break;
        }
      }

      log.info("增量同步微信商品完成，类型: {}, 检查商品总数: {}, 实际同步商品数量: {}", type, totalChecked, totalSynced);
      return true;

    } catch (Exception e) {
      log.error("增量同步微信商品异常，类型: {}", type, e);
      return false;
    }
  }

  @Override
  public WechatProductListDto getProductList(String type, Integer offset, Integer limit) {
    try {
      // 获取访问令牌
      WechatAccessTokenDto accessToken = wechatApiService.getAccessToken(type);
      if (accessToken == null || !accessToken.isSuccess()) {
        log.error("获取访问令牌失败，类型: {}", type);
        return null;
      }

      String token = accessToken.getAccessToken();
      // 使用nextKey方式，首次调用传null作为nextKey，pageSize使用limit参数
      return wechatApiService.getProductListWithNextKey(type, token, limit, null);
    } catch (Exception e) {
      log.error("获取商品列表异常，类型: {}", type, e);
      return null;
    }
  }

  @Override
  public WechatProductDetailDto getProductDetail(String type, String productId) {
    try {
      // 先从数据库查询
      WechatProductDetail entity =
          wechatProductDetailGateway.findByTypeAndProductId(type, productId);
      if (entity != null) {
        log.info("从数据库获取商品详情，类型: {}, 商品ID: {}", type, productId);
        return WechatProductDetailConverter.entityToDto(entity);
      }

      // 数据库中没有，从API获取
      log.info("数据库中未找到商品详情，从API获取，类型: {}, 商品ID: {}", type, productId);
      WechatAccessTokenDto accessToken = wechatApiService.getAccessToken(type);
      if (accessToken == null || !accessToken.isSuccess()) {
        log.error("获取访问令牌失败，类型: {}", type);
        return null;
      }

      String token = accessToken.getAccessToken();
      WechatProductDetailDto productDetail =
          wechatApiService.getProductDetail(type, token, productId);

      // 如果获取成功，保存到数据库
      if (productDetail != null) {
        WechatProductDetail newEntity =
            WechatProductDetailConverter.dtoToEntity(productDetail, type);
        if (newEntity != null) {
          wechatProductDetailGateway.save(newEntity);
          log.info("商品详情已保存到数据库，类型: {}, 商品ID: {}", type, productId);
        }
      }

      return productDetail;
    } catch (Exception e) {
      log.error("获取商品详情异常，类型: {}, 商品ID: {}", type, productId, e);
      return null;
    }
  }

  @Override
  public List<WechatProductDetailDto> getProductDetails(String type, List<String> productIds) {
    if (productIds == null || productIds.isEmpty()) {
      return new ArrayList<>();
    }

    try {
      WechatAccessTokenDto accessToken = wechatApiService.getAccessToken(type);
      if (accessToken == null || !accessToken.isSuccess()) {
        log.error("获取访问令牌失败，类型: {}", type);
        return new ArrayList<>();
      }

      String token = accessToken.getAccessToken();

      List<CompletableFuture<WechatProductDetailDto>> futures = new ArrayList<>();

      for (String productId : productIds) {
        CompletableFuture<WechatProductDetailDto> future =
            CompletableFuture.supplyAsync(
                () -> {
                  try {
                    return wechatApiService.getProductDetail(type, token, productId);
                  } catch (Exception e) {
                    log.error("异步获取商品详情异常，类型: {}, 商品ID: {}", type, productId, e);
                    return null;
                  }
                },
                executorService);

        futures.add(future);
      }

      CompletableFuture<Void> allFutures =
          CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]));

      try {
        allFutures.get(1, TimeUnit.MINUTES);
      } catch (Exception e) {
        log.error("等待异步任务完成异常，类型: {}", type, e);
      }

      List<WechatProductDetailDto> results = new ArrayList<>();
      for (CompletableFuture<WechatProductDetailDto> future : futures) {
        try {
          WechatProductDetailDto result = future.get();
          if (result != null) {
            results.add(result);
          }
        } catch (Exception e) {
          log.error("获取异步任务结果异常，类型: {}", type, e);
        }
      }

      return results;

    } catch (Exception e) {
      log.error("批量获取商品详情异常，类型: {}", type, e);
      return new ArrayList<>();
    }
  }
}
