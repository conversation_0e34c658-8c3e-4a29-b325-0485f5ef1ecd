package com.daddylab.supplier.item.application.shop;

import com.alibaba.fastjson2.JSON;
import com.daddylab.supplier.item.application.shipinghao.dto.WechatAccessTokenDto;
import com.daddylab.supplier.item.application.shipinghao.service.WechatApiService;
import com.daddylab.supplier.item.common.ExceptionPlusFactory;
import com.daddylab.supplier.item.common.enums.ErrorCode;
import com.daddylab.supplier.item.domain.common.enums.Platform;
import com.daddylab.supplier.item.infrastructure.doudian.DouDianCommon;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.OpenApp;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.Shop;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ShopAuthorization;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ShopAuthorizationApp;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IOpenAppService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IShopAuthorizationAppService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IShopAuthorizationService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IShopService;
import com.daddylab.supplier.item.infrastructure.third.config.KuaiShouConfig;
import com.daddylab.supplier.item.infrastructure.third.config.RedBookConfig;
import com.daddylab.supplier.item.infrastructure.third.kuaishou.KuaiShouOAuthService;
import com.daddylab.supplier.item.infrastructure.third.redbook.RedBookOAuthService;
import com.daddylab.supplier.item.infrastructure.utils.DateUtil;
import com.doudian.open.core.AccessToken;
import com.doudian.open.core.AccessTokenBuilder;
import com.doudian.open.core.DoudianOpClientHolder;
import com.doudian.open.core.DoudianOpConfig;
import com.kuaishou.merchant.open.api.response.oauth.KsAccessTokenResponse;
import com.xiaohongshu.fls.opensdk.entity.oauth.response.GetAccessTokenResponse;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Optional;

/**
 * <AUTHOR>
 * @since 2024/4/9
 */
@Service
@RequiredArgsConstructor
public class ShopAuthorizationBizServiceImpl implements ShopAuthorizationBizService {
  private final IShopService shopService;
  private final IShopAuthorizationService shopAuthorizationService;
  private final IShopAuthorizationAppService shopAuthorizationAppService;
  private final IOpenAppService openAppService;
  private final RedBookOAuthService redBookOAuthService;
  private final KuaiShouOAuthService kuaiShouOAuthService;
  private final KuaiShouConfig kuaiShouConfig;
  private final RedBookConfig redBookConfig;
  private final DouDianCommon douDianCommon;
  private final WechatApiService wechatApiService;

  @Override
  public String authorizationCallback(Platform platform, String code, String state) {
    final Long currentTime = DateUtil.currentTime();
    final ShopAuthorization shopAuthorization = new ShopAuthorization();
    shopAuthorization.setSn(state);
    shopAuthorization.setPlatform(platform);

    switch (platform) {
      case DOUDIAN:
        {
          return "抖店需前往抖店开放平台手动进行授权管理";
        }

      case KUAISHOU:
        {
          final KsAccessTokenResponse accessToken = kuaiShouOAuthService.getAccessToken(code);
          shopAuthorization.setAccessToken(accessToken.getAccessToken());
          shopAuthorization.setExpiredAt(accessToken.getExpiresIn() + currentTime);
          shopAuthorization.setRefreshToken(accessToken.getRefreshToken());
          shopAuthorization.setRefreshTokenExpiredAt(
              accessToken.getRefreshTokenExpiresIn() + currentTime);
          shopAuthorization.setScopes(
              Optional.ofNullable(accessToken.getScopes())
                  .map(scopes -> String.join(",", scopes))
                  .orElse(""));
          shopAuthorization.setTokenData(JSON.toJSONString(accessToken));
          shopAuthorizationService.saveOrUpdateShopAuthorization(shopAuthorization);
          return "授权完成";
        }

      case XIAOHONGSHU:
        {
          final GetAccessTokenResponse accessToken = redBookOAuthService.getAccessToken(code);
          shopAuthorization.setAccessToken(accessToken.getAccessToken());
          shopAuthorization.setExpiredAt(accessToken.getAccessTokenExpiresAt() / 1000);
          shopAuthorization.setRefreshToken(accessToken.getRefreshToken());
          shopAuthorization.setRefreshTokenExpiredAt(accessToken.getRefreshTokenExpiresAt() / 1000);
          shopAuthorization.setScopes("");
          shopAuthorization.setTokenData(JSON.toJSONString(accessToken));
          shopAuthorizationService.saveOrUpdateShopAuthorization(shopAuthorization);
          return "授权完成";
        }

      default:
        return "不支持的平台";
    }
  }

  @Override
  public String authorize(String shopNo) {
    final Shop shop =
        shopService
            .getBySn(shopNo)
            .orElseThrow(
                () -> ExceptionPlusFactory.bizException(ErrorCode.DATA_NOT_FOUND, "店铺不存在"));

    ShopAuthorization shopAuthorization =
        shopAuthorizationService.getByShopNo(shop.getSn()).orElse(null);
    if (shopAuthorization != null && shopAuthorization.isNotExpired()) {
      return "授权完成";
    }

    if (shopAuthorization == null) {
      shopAuthorization = new ShopAuthorization();
      shopAuthorization.setPlatform(shop.getPlatform());
      shopAuthorization.setSn(shop.getSn());
    }

    final Platform platform = shop.getPlatform();
    switch (platform) {
      case DOUDIAN:
        {
          Optional<ShopAuthorizationApp> authorizationAppOptional =
              shopAuthorizationAppService.getByShopNo(shopNo);
          if (!authorizationAppOptional.isPresent()) {
            return "未配置开放平台授权应用";
          }
          final ShopAuthorizationApp authorizationApp = authorizationAppOptional.get();
          final OpenApp openApp = openAppService.getById(authorizationApp.getOpenAppId());

          final DoudianOpConfig doudianOpConfig = new DoudianOpConfig();
          doudianOpConfig.setAppKey(openApp.getAppKey());
          doudianOpConfig.setAppSecret(openApp.getAppSecret());

          final AccessToken accessToken =
              AccessTokenBuilder.build(
                  doudianOpConfig,
                  DoudianOpClientHolder.getClient(),
                  Long.parseLong(authorizationApp.getPfmShopId()));
          if (!accessToken.isSuccess()) {
            return "抖店获取token失败:" + accessToken.getSubCode() + " " + accessToken.getSubMsg();
          }
          shopAuthorization.setAccessToken(accessToken.getAccessToken());
          shopAuthorization.setExpiredAt(accessToken.getExpireIn() + DateUtil.currentTime());
          shopAuthorization.setRefreshToken(accessToken.getRefreshToken());
          shopAuthorization.setRefreshTokenExpiredAt(shopAuthorization.getExpiredAt() * 2);
          shopAuthorization.setScopes(accessToken.getScope());
          shopAuthorization.setTokenData(JSON.toJSONString(accessToken));
          shopAuthorizationService.saveOrUpdateShopAuthorization(shopAuthorization);
          return "授权完成";
        }

      case KUAISHOU:
        {
          final String redirectUrl = kuaiShouConfig.getAuthorizationCallbackUrl();
          return String.format(
              "<a href=\"%s\">点击跳转授权</a>",
              kuaiShouOAuthService.getAuthorizationUrl(redirectUrl, shop.getSn()));
        }

      case XIAOHONGSHU:
        {
          final String redirectUrl = redBookConfig.getAuthorizationCallbackUrl();
          return String.format(
              "<a href=\"%s\">点击跳转授权</a>",
              redBookOAuthService.getAuthorizationUrl(redirectUrl, shop.getSn()));
        }

      case WECHAT_VIDEO:
        WechatAccessTokenDto tokenDto;
        if (shop.getName().contains("老爸评测DADDYLAB")) {
          tokenDto = wechatApiService.getAccessToken("daddylab");
        } else if (shop.getName().contains("老爸评测会员店")) {
          tokenDto = wechatApiService.getAccessToken("member");
        } else {
          throw ExceptionPlusFactory.bizException(
              ErrorCode.OPERATION_REJECT, "微信视频号暂不支持此店铺授权，店铺编码：" + shopNo);
        }
        shopAuthorization.setAccessToken(tokenDto.getAccessToken());
        shopAuthorization.setExpiredAt(tokenDto.getExpiresIn() + DateUtil.currentTime());
        shopAuthorization.setTokenData(JSON.toJSONString(tokenDto));
        shopAuthorizationService.saveOrUpdateShopAuthorization(shopAuthorization);
        return "授权完成";

      default:
        throw ExceptionPlusFactory.bizException(ErrorCode.OPERATION_REJECT, "暂不支持此平台店铺授权");
    }
  }
}
