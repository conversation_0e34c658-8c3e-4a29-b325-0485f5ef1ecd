package com.daddylab.supplier.item.application.shipinghao.dto.wechatProduct;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data; /** 额外服务信息 */
@Data
public class ExtraService {
  /** 七天无理由退货：0-不支持，1-支持 */
  @JsonProperty("seven_day_return")
  private Integer sevenDayReturn;

  /** 先用后付：0-不支持，1-支持 */
  @JsonProperty("pay_after_use")
  private Integer payAfterUse;

  /** 运费险：0-不支持，1-支持 */
  @JsonProperty("freight_insurance")
  private Integer freightInsurance;

  /** 破损包赔：0-不支持，1-支持 */
  @JsonProperty("damage_guarantee")
  private Integer damageGuarantee;

  /** 假一赔三：0-不支持，1-支持 */
  @JsonProperty("fake_one_pay_three")
  private Integer fakeOnePayThree;
}
