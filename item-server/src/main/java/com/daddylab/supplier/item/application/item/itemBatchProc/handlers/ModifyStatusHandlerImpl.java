package com.daddylab.supplier.item.application.item.itemBatchProc.handlers;

import com.alibaba.cola.dto.PageResponse;
import com.alibaba.cola.dto.Response;
import com.daddylab.supplier.item.application.item.ItemBizService;
import com.daddylab.supplier.item.application.item.itemBatchProc.ItemBatchProcHandler;
import com.daddylab.supplier.item.controller.item.dto.ItemPageQuery;
import com.daddylab.supplier.item.controller.item.dto.ItemPageVo;
import com.daddylab.supplier.item.infrastructure.enums.IEnum;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ItemBatchProc;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.ItemStatus;
import com.daddylab.supplier.item.infrastructure.utils.JsonUtil;
import com.daddylab.supplier.item.types.itemBatchProc.ItemBatchProcType;
import com.daddylab.supplier.item.types.itemBatchProc.ModifyStatusCmd;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import org.slf4j.event.Level;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2023/12/25
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class ModifyStatusHandlerImpl implements ItemBatchProcHandler {
  private final ItemBizService itemBizService;

  @Override
  public void handle(ItemBatchProc batchProc, Synchronize synchronize) {
    final ModifyStatusCmd cmd = JsonUtil.parse(batchProc.getCmd(), ModifyStatusCmd.class);
    Objects.requireNonNull(cmd, "命令解析异常");

    final Integer newStatus = cmd.getStatus();
    final ItemStatus newStatusEnum =
        IEnum.getEnumOptByValue(ItemStatus.class, newStatus)
            .orElseThrow(() -> new IllegalArgumentException("商品状态不合法"));

    int totalPage = -1;
    int pageIndex = 1;
    final ItemPageQuery query = cmd.getQuery();
    query.setPageSize(100);
    while (true) {
      query.setPageIndex(pageIndex);
      final PageResponse<ItemPageVo> itemPageVoPageResponse = itemBizService.queryPage(query);
      if (totalPage == -1) {
        totalPage = itemPageVoPageResponse.getTotalPages();
      }
      final List<ItemPageVo> data = itemPageVoPageResponse.getData();
      if (data.isEmpty()) {
        break;
      }
      for (ItemPageVo itemPageVo : data) {
        final Long itemId = itemPageVo.getId();
        try {
          final Response response =
              itemBizService.updateStatus(
                  itemId,
                  newStatusEnum,
                  cmd.getDownFrameTime(),
                  cmd.getDownFrameReason(),
                  "商品批量处理");
          if (response.isSuccess()) {
            synchronize.log(Level.INFO, "商品 %s 状态修改完成", itemId);
          } else {
            synchronize.log(Level.ERROR, response.getErrMessage());
          }
        } catch (Exception e) {
          synchronize.log(Level.ERROR, e.getMessage());
          log.error("【商品批量处理】更新商品状态异常:{}", e.getMessage(), e);
        }
      }
      synchronize.setProcess(totalPage == 0 ? 100 : (pageIndex / totalPage) * 100);
      pageIndex++;
    }
  }

  @Override
  public boolean isSupport(ItemBatchProc batchProc) {
    return ItemBatchProcType.MODIFY_STATUS.getValue().equals(batchProc.getType());
  }
}
