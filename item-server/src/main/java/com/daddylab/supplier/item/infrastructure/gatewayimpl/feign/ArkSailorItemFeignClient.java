package com.daddylab.supplier.item.infrastructure.gatewayimpl.feign;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.daddylab.ark.sailor.item.common.base.vo.Result;
import com.daddylab.ark.sailor.item.domain.query.item.ItemPageQuery;
import com.daddylab.ark.sailor.item.domain.vo.category.CategoryListVO;
import com.daddylab.ark.sailor.item.domain.vo.item.ItemListVO;
import com.daddylab.ark.sailor.item.domain.vo.item.ItemSkuVO;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.feign.dto.*;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * @Author: <PERSON> @Date: 2022/9/19 10:37 @Description: 调用电商商品服务（ark-sailor-item）的 feignClient
 */
@FeignClient(name = "arkSailorItemFeignClient", url = "${ark-sailor-item-server.url}", fallbackFactory = ArkSailorItemFeignClientFallbackFactory.class)
public interface ArkSailorItemFeignClient {
    /**
     * 商品匹配查询
     *
     * @param param 参数
     * @return ArkSailorItemResponse<List < ItemSyncMiniProgramMatchResult>>
     */
    @RequestMapping(value = "/erp/item/sync/match", method = RequestMethod.PUT)
    ArkSailorItemResponse<List<ItemSyncMiniProgramMatchResult>> syncMatch(
            @RequestBody List<ItemSyncMiniProgramMatchParam> param);

    /**
     * 商品匹配查询V2（多店改造）
     *
     * @param param 参数
     * @return ArkSailorItemResponse<List < ItemSyncMiniProgramMatchResult>>
     */
    @RequestMapping(value = "/erp/item/sync/match/v2", method = RequestMethod.PUT)
    ArkSailorItemResponse<List<ItemSyncMiniProgramMatchResult>> syncMatchV2(
            @RequestBody ItemSyncMiniProgramMatchParamV2 param);

    /**
     * 同步商品
     *
     * @param param 参数
     * @return ArkSailorItemResponse<Boolean>
     */
    @RequestMapping(value = "/erp/item/sync", method = RequestMethod.PUT)
    ArkSailorItemResponse<Boolean> sync(@RequestBody List<ItemSyncMiniProgramRequest> param);

    /**
     * 同步商品V2（多店改造）
     *
     * @param param 参数
     * @return ArkSailorItemResponse<Boolean>
     */
    @RequestMapping(value = "/erp/item/sync/v2", method = RequestMethod.PUT)
    ArkSailorItemResponse<Boolean> syncV2(@RequestBody ItemSyncMiniProgramRequestV2 param);

    @ApiOperation(value = "商品分页查询", notes = "商品分页查询", httpMethod = "POST")
    @PostMapping("/erp/item/itemPageQuery")
    Result<Page<ItemListVO>> itemPageQuery(@RequestBody ItemPageQuery query);

    @ApiOperation(value = "根据商品ID批量查询SKU", notes = "根据商品ID批量查询SKU", httpMethod = "POST")
    @PostMapping("/erp/item/itemSkuQueryByItemIds")
    Result<List<ItemSkuVO>> itemSkuQueryByItemIds(@RequestBody List<Long> itemIds);


    @ApiOperation(value = "获取itemSku分页", notes = "获取itemSku分页", httpMethod = "POST")
    @PostMapping("/erp/item/getSkuPageList")
    Result<Page<SkuPageListItemVO>> getSkuPageList(@RequestBody ItemSkuPageQuery itemSkuPageQuery);

    /**
     * 更新(自研电商)Sku库存
     *
     * @param erpItemSkuUpdateStockParam ErpItemSkuUpdateStockParam
     * @return com.daddylab.ark.sailor.item.common.base.vo.Result<java.lang.Boolean>
     * @date 2024/3/5 09:21
     * <AUTHOR>
     */
    @ApiOperation(value = "更新库存", notes = "更新库存", httpMethod = "POST")
    @PostMapping("/erp/item/updateStock")
    Result<Boolean> updateStock(@Valid @RequestBody ErpItemSkuUpdateStockParam erpItemSkuUpdateStockParam);

    /**
     * 批量获取sku库存
     *
     * @param skuIds List<Long>
     * @return com.daddylab.ark.sailor.item.common.base.vo.Result<java.util.List < com.daddylab.supplier.item.infrastructure.gatewayimpl.feign.dto.SkuStockVo>>
     * @date 2024/3/5 09:25
     * <AUTHOR>
     */
    @ApiOperation(value = "批量查询库存", notes = "批量查询库存", httpMethod = "POST")
    @PostMapping("/erp/item/getSkuStockList")
    Result<List<SkuStockVo>> getSkuStocks(@RequestBody @NotEmpty(message = "skuId不能为空") List<Long> skuIds);

    /**
     * 获取单个sku库存
     *
     * @param skuId Long
     * @return com.daddylab.ark.sailor.item.common.base.vo.Result<com.daddylab.ark.sailor.item.domain.po.Stock>
     * @date 2024/3/5 09:25
     * <AUTHOR>
     */
    @ApiOperation(value = "获取库存", notes = "获取库存", httpMethod = "GET")
    @GetMapping("/erp/item/getSkuStock")
    Result<SkuStockVo> getSkuStock(@RequestParam("skuId") Long skuId);

    @ApiOperation(value = "查询卖家类目列表", notes = "查询卖家类目列表", httpMethod = "GET")
    @GetMapping("/erp/seller-category/list")
    Result<List<CategoryListVO>> getCategoryList(@SpringQueryMap CategoryListQuery categoryQuery);

    @ApiOperation(value = "查询卖家最末级类目列表", notes = "查询卖家最末级类目列表", httpMethod = "GET")
    @GetMapping("/erp/seller-category/list/last")
    Result<List<CategoryListVO>> getLastCategoryList(@RequestParam("sellerId") Long sellerId);
}
