package com.daddylab.supplier.item.application.shipinghao.dto.wechatProduct;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List; /** 自定义配置 */
@Data
public class CustomConfig {
  /** 自定义类型列表 */
  @JsonProperty("custom_type")
  private List<String> customType;

  /** 自定义文本最大长度 */
  @JsonProperty("custom_text_max_length")
  private Integer customTextMaxLength;

  /** 自定义文本输入类型 */
  @JsonProperty("custom_text_input_type")
  private Integer customTextInputType;

  /** 自定义文本方向 */
  @JsonProperty("custom_text_direction")
  private Integer customTextDirection;

  /** 自定义文本字体大小 */
  @JsonProperty("custom_text_font_size")
  private Integer customTextFontSize;

  /** 自定义文本颜色 */
  @JsonProperty("custom_text_color")
  private String customTextColor;

  /** 自定义文本输入类型列表 */
  @JsonProperty("custom_text_input_types")
  private List<String> customTextInputTypes;

  /** 是否开启自定义 */
  @JsonProperty("open_custom")
  private Boolean openCustom;

  /** 描述图片列表 */
  @JsonProperty("desc_img_list")
  private List<String> descImgList;

  /** 发货时间 */
  @JsonProperty("delivery_time")
  private Integer deliveryTime;

  /** 预览类型 */
  @JsonProperty("preview_type")
  private Integer previewType;
}
