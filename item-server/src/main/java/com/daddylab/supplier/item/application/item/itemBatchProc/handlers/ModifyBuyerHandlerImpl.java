package com.daddylab.supplier.item.application.item.itemBatchProc.handlers;

import com.alibaba.cola.dto.PageResponse;
import com.daddylab.supplier.item.application.item.ItemBizService;
import com.daddylab.supplier.item.application.item.itemBatchProc.ItemBatchProcHandler;
import com.daddylab.supplier.item.controller.item.dto.ItemPageQuery;
import com.daddylab.supplier.item.controller.item.dto.ItemPageVo;
import com.daddylab.supplier.item.domain.item.gateway.BuyerGateway;
import com.daddylab.supplier.item.domain.operateLog.enums.OperateLogTarget;
import com.daddylab.supplier.item.domain.operateLog.service.OperateLogDomainService;
import com.daddylab.supplier.item.domain.user.gateway.UserGateway;
import com.daddylab.supplier.item.infrastructure.common.UserContext;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ItemBatchProc;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IItemProcurementService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.user.StaffInfo;
import com.daddylab.supplier.item.infrastructure.kingdee.KingDeeTemplate;
import com.daddylab.supplier.item.infrastructure.utils.DiffUtil;
import com.daddylab.supplier.item.infrastructure.utils.JsonUtil;
import com.daddylab.supplier.item.types.itemBatchProc.ItemBatchProcType;
import com.daddylab.supplier.item.types.itemBatchProc.ModifyBuyerCmd;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import org.slf4j.event.Level;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2023/11/16
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class ModifyBuyerHandlerImpl implements ItemBatchProcHandler {
    private final ItemBizService itemBizService;
    private final IItemProcurementService itemProcurementService;
    private final KingDeeTemplate kingDeeTemplate;
    private final OperateLogDomainService operateLogDomainService;
    private final BuyerGateway buyerGateway;
    private final UserGateway userGateway;

    @Override
    public void handle(ItemBatchProc batchProc, Synchronize synchronize) {
        final ModifyBuyerCmd cmd = JsonUtil.parse(batchProc.getCmd(), ModifyBuyerCmd.class);
        Objects.requireNonNull(cmd, "命令解析异常");

        final StaffInfo staffInfo = userGateway.queryStaffInfoById(cmd.getBuyerUserId());
        Objects.requireNonNull(staffInfo, "采购员用户信息查询异常");

        final Long buyerId =
                buyerGateway.saveOrUpdateBuyer(staffInfo.getUserId(), staffInfo.getNickname());

        int totalPage = -1;
        int pageIndex = 1;
        final ItemPageQuery query = cmd.getQuery();
        query.setPageSize(100);
        while (true) {
            query.setPageIndex(pageIndex);
            final PageResponse<ItemPageVo> itemPageVoPageResponse =
                    itemBizService.queryPage(query);
            if (totalPage == -1) {
                totalPage = itemPageVoPageResponse.getTotalPages();
            }
            final List<ItemPageVo> data = itemPageVoPageResponse.getData();
            if (data.isEmpty()) {
                break;
            }
            for (ItemPageVo itemPageVo : data) {
                final Long itemId = itemPageVo.getId();
                if (!Objects.equals(itemPageVo.getItemBuyerUserId(), cmd.getBuyerUserId())) {
                    itemProcurementService.setBuyerId(itemId, buyerId);
                    operateLogDomainService.addOperatorLog(
                            UserContext.getUserId(),
                            OperateLogTarget.ITEM,
                            itemId,
                            String.format(
                                    "【商品批量处理】采购员从 %s 修改为 %s",
                                    itemPageVo.getItemBuyerNickName(), staffInfo.getNickname()),
                            new Object[] {
                                    new DiffUtil.ChangePropertyObj(
                                            "buyerId",
                                            itemPageVo.getItemBuyerUserId(),
                                            staffInfo.getUserId())
                            });
                    synchronize.log(Level.INFO, "商品 %s 采购员修改完成", itemId);
                } else {
                    synchronize.log(Level.WARN, "商品 %s 采购员无需变更", itemId);
                }
                try {
                    itemBizService.notifyBuyerOrQcChange(itemId);
                    synchronize.log(Level.INFO, "商品 %s 采购员&QC修改同步到P系统成功", itemId);
                } catch (Exception e) {
                    synchronize.log(Level.ERROR, "商品 %s 采购员&QC修改同步到P系统失败:{}", itemId, e.getMessage());
                    log.error("【商品批量处理】商品 {} 采购员&QC修改同步到P系统失败", itemId, e);
                }

                try {
                    kingDeeTemplate.syncItem(itemId);
                    synchronize.log(Level.INFO, "商品 %s 已同步至金蝶", itemId);
                } catch (Exception e) {
                    synchronize.log(Level.WARN, "商品 %s 推送金蝶失败:{}", e.getMessage());
                    log.error("【商品批量处理】商品 {} 推送金蝶失败", itemId, e);
                }
            }
            synchronize.setProcess(totalPage == 0 ? 100 : (pageIndex / totalPage) * 100);
            pageIndex++;
        }
    }

    @Override
    public boolean isSupport(ItemBatchProc batchProc) {
        return ItemBatchProcType.MODIFY_BUYER.getValue().equals(batchProc.getType());
    }
}
