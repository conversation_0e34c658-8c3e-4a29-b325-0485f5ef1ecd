package com.daddylab.supplier.item.application.platformItemSkuInventory.support;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.daddylab.supplier.item.application.platformItemSkuInventory.domain.params.PlatformSkuSyncInfo;
import com.daddylab.supplier.item.application.shipinghao.dto.WechatAccessTokenDto;
import com.daddylab.supplier.item.application.shipinghao.dto.WechatProductDetailDto;
import com.daddylab.supplier.item.application.shipinghao.dto.WechatProductListDto;
import com.daddylab.supplier.item.application.shipinghao.dto.wechatProduct.Attribute;
import com.daddylab.supplier.item.application.shipinghao.dto.wechatProduct.Sku;
import com.daddylab.supplier.item.application.shipinghao.dto.wechatProduct.WeChatProduct;
import com.daddylab.supplier.item.application.shipinghao.service.WechatApiService;
import com.daddylab.supplier.item.application.shipinghao.service.WechatProductSyncService;
import com.daddylab.supplier.item.domain.common.enums.Platform;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ShopAuthorization;
import com.daddylab.supplier.item.infrastructure.utils.JsonUtil;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.core.Job;
import org.springframework.batch.core.JobExecution;
import org.springframework.batch.core.JobParametersBuilder;
import org.springframework.batch.core.Step;
import org.springframework.batch.core.job.builder.FlowBuilder;
import org.springframework.batch.core.job.flow.Flow;
import org.springframework.batch.core.job.flow.support.SimpleFlow;
import org.springframework.batch.item.ItemProcessor;
import org.springframework.batch.item.ItemReader;
import org.springframework.batch.item.ItemWriter;
import org.springframework.core.task.SimpleAsyncTaskExecutor;
import org.springframework.stereotype.Service;

/**
 * 视频号平台商品同步服务实现
 *
 * <AUTHOR>
 * @date 2024-12-19
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class WechatVideoItemSyncServiceImpl
    extends AbstractPlatformItemSkuSync<
        WechatVideoItemSyncServiceImpl.ProductItemWrapper, List<PlatformSkuSyncInfo>> {

  private final WechatProductSyncService wechatProductSyncService;
  private final WechatApiService wechatApiService;

  @Override
  public Platform defaultType() {
    return Platform.WECHAT_VIDEO;
  }

  @Override
  public void fullDoseSync() {
    try {
      JobExecution jobExecution = executeFullDoseSync();
      log.info("[视频号平台商品同步]任务执行结果={}", jobExecution.getStatus());
    } catch (Exception e) {
      log.error("[视频号平台商品同步]任务运行异常", e);
    }
  }

  /** 执行全量同步并返回JobExecution（用于测试监控） */
  public JobExecution executeFullDoseSync() throws Exception {
    // 获取所有未过期的视频号店铺授权
    final List<ShopAuthorization> shopAuthorizations =
        shopAuthorizationService.listNotExpiredAuthorizations(Platform.WECHAT_VIDEO);

    log.info(
        "[平台商品同步][视频号]开始同步，店铺={}",
        shopAuthorizations.stream().map(ShopAuthorization::getSn).collect(Collectors.toList()));

    final String platform = defaultType().getDesc();
    final SimpleAsyncTaskExecutor executor =
        new SimpleAsyncTaskExecutor(defaultType().name() + "-fullDoseSync");
    executor.setConcurrencyLimit(2); // 微信API限制，并发不宜过高

    final ArrayList<Flow> stepFlows = new ArrayList<>();

    for (ShopAuthorization shopAuthorization : shopAuthorizations) {
      final String sn = shopAuthorization.getSn();

      // 为每个店铺的每个类型（daddylab/member）创建同步步骤
      String[] types = {"daddylab", "member"}; // 根据微信配置的两种类型

      for (String type : types) {
        final Step step =
            buildStep(
                getFullDoseItemReader(shopAuthorization, type),
                platformItemSyncConfig.getChunkSize(),
                sn + "-" + type);
        final SimpleFlow shopFlow =
            new FlowBuilder<SimpleFlow>(platform + sn + type).start(step).build();
        stepFlows.add(shopFlow);
      }
    }

    final SimpleFlow flow =
        new FlowBuilder<SimpleFlow>(defaultType().name() + "-fullDoseSync")
            .split(executor)
            .add(stepFlows.toArray(new Flow[0]))
            .next(
                stepBuilderFactory
                    .get(String.format("[%s]-StatisticsStep", defaultType().getDesc()))
                    .tasklet(getStatisticsTasklet())
                    .build())
            .build();

    final Job job =
        jobBuilderFactory
            .get(PLATFORM_ITEM_SKU_SYNC_JOB + "-" + defaultType().name())
            .start(flow)
            .build()
            .build();

    JobParametersBuilder parametersBuilder = new JobParametersBuilder();
    parametersBuilder.addDate("date", new Date());

    return jobLauncher.run(job, parametersBuilder.toJobParameters());
  }

  /** 构建全量同步的ItemReader（自定义游标分页） */
  public ItemReader<ProductItemWrapper> getFullDoseItemReader(
      ShopAuthorization shopAuthorization, String type) {
    return new WechatCursorPagingItemReader(shopAuthorization, type, 30);
  }

  /** 自定义的微信游标分页ItemReader 正确处理微信API的next_key分页机制 */
  private class WechatCursorPagingItemReader implements ItemReader<ProductItemWrapper> {
    private final ShopAuthorization shopAuthorization;
    private final String type;
    private final int pageSize;

    private String currentNextKey = null;
    private List<ProductItemWrapper> currentBatch = new ArrayList<>();
    private int currentIndex = 0;
    private boolean hasMoreData = true;

    public WechatCursorPagingItemReader(
        ShopAuthorization shopAuthorization, String type, int pageSize) {
      this.shopAuthorization = shopAuthorization;
      this.type = type;
      this.pageSize = Math.min(pageSize, 30); // 微信API限制
    }

    @Override
    public ProductItemWrapper read() throws Exception {
      // 如果当前批次已读完且还有更多数据，则获取下一批
      if (currentIndex >= currentBatch.size() && hasMoreData) {
        loadNextBatch();
      }

      // 如果还有数据则返回，否则返回null表示读取完成
      if (currentIndex < currentBatch.size()) {
        return currentBatch.get(currentIndex++);
      }

      return null;
    }

    private void loadNextBatch() {
      try {
        log.info("[视频号]加载下一批数据，类型: {}, next_key: {}", type, currentNextKey);

        // 获取访问令牌
        WechatAccessTokenDto accessToken = wechatApiService.getAccessToken(type);
        if (accessToken == null || !accessToken.isSuccess()) {
          log.error("[视频号]获取访问令牌失败，类型: {}", type);
          hasMoreData = false;
          return;
        }

        // 使用next_key获取商品列表
        WechatProductListDto productList =
            wechatApiService.getProductListWithNextKey(
                type, accessToken.getAccessToken(), pageSize, currentNextKey);

        if (productList == null || CollUtil.isEmpty(productList.getProductIds())) {
          log.info("[视频号]没有更多商品数据，类型: {}", type);
          hasMoreData = false;
          return;
        }

        log.info(
            "[视频号][商品列表]获取商品ID列表成功，类型: {}, 数量: {}, 总数: {}",
            type,
            productList.getProductIds().size(),
            productList.getTotalNum());

        // 批量获取商品详情
        List<WechatProductDetailDto> productDetails =
            wechatProductSyncService.getProductDetails(type, productList.getProductIds());

        log.info(
            "[视频号][商品详情]批量获取商品详情完成，类型: {}, 请求数量: {}, 成功获取数量: {}",
            type,
            productList.getProductIds().size(),
            productDetails != null ? productDetails.size() : 0);

        // 重置当前批次
        currentBatch.clear();
        currentIndex = 0;

        int successCount = 0;
        int failCount = 0;

        // 转换为ProductItemWrapper
        assert productDetails != null;
        for (WechatProductDetailDto detail : productDetails) {
          if (detail != null && detail.getProduct() != null) {
            ProductItemWrapper wrapper = new ProductItemWrapper();
            wrapper.setProduct(detail.getProduct());
            wrapper.setShopNo(shopAuthorization.getSn());
            wrapper.setType(type);
            currentBatch.add(wrapper);
            successCount++;

//            log.debug(
//                "[视频号][商品包装]成功包装商品，ID: {}, 标题: {}, SKU数量: {}",
//                detail.getProduct().getProductId(),
//                detail.getProduct().getTitle(),
//                detail.getProduct().getSkus() != null ? detail.getProduct().getSkus().size() : 0);
          } else {
            failCount++;
            log.warn("[视频号][商品包装]商品详情为空或商品对象为空，跳过");
          }
        }

        // 更新next_key和分页状态
        currentNextKey = productList.getNextKey();
        hasMoreData = (currentNextKey != null && !currentNextKey.isEmpty());

        log.info(
            "[视频号]成功加载批次数据，类型: {}, 商品数量: {}, 成功包装: {}, 失败: {}, 下一页key: {}, 还有更多: {}",
            type,
            currentBatch.size(),
            successCount,
            failCount,
            currentNextKey,
            hasMoreData);

      } catch (Exception e) {
        log.error("[视频号]加载批次数据失败，类型: {}", type, e);
        hasMoreData = false;
      }
    }
  }

  /**
   * 构建全量同步的ItemReader（兼容旧版本）
   *
   * @deprecated 建议使用getFullDoseItemReader方法，该方法正确处理next_key分页
   */
  @Deprecated
  public ItemReader<ProductItemWrapper> getFullDoseItemReaderOld(
      ShopAuthorization shopAuthorization, String type) {
    return getItemReader(
        iPage -> {
          try {
            int pageSize = (int) iPage.getSize();
            pageSize = Math.min(pageSize, 30); // 微信API限制最大30条

            // 使用正确的next_key分页方式
            // 注意：这里的分页逻辑需要特殊处理，因为微信API使用游标分页而不是页码分页
            String nextKey = null;
            if (iPage.getCurrent() > 1) {
              // 如果不是第一页，我们需要从某个地方获取next_key
              // 但是在Spring Batch的分页机制中，这比较复杂
              // 暂时先获取第一页，实际的完整分页需要重新设计
              log.warn("微信API使用游标分页，Spring Batch的页码分页需要特殊处理。当前页: {}", iPage.getCurrent());
            }

            // 获取访问令牌
            WechatAccessTokenDto accessToken = wechatApiService.getAccessToken(type);
            if (accessToken == null || !accessToken.isSuccess()) {
              log.error("获取访问令牌失败，类型: {}", type);
              iPage.setRecords(Collections.emptyList());
              iPage.setTotal(0);
              return iPage;
            }

            // 使用新的next_key分页方法
            WechatProductListDto productList =
                wechatApiService.getProductListWithNextKey(
                    type, accessToken.getAccessToken(), pageSize, nextKey);

            if (productList == null || CollUtil.isEmpty(productList.getProductIds())) {
              iPage.setRecords(Collections.emptyList());
              iPage.setTotal(0);
              return iPage;
            }

            // 批量获取商品详情
            List<WechatProductDetailDto> productDetails =
                wechatProductSyncService.getProductDetails(type, productList.getProductIds());

            List<ProductItemWrapper> wrappers = new ArrayList<>();
            for (WechatProductDetailDto detail : productDetails) {
              if (detail != null && detail.getProduct() != null) {
                ProductItemWrapper wrapper = new ProductItemWrapper();
                wrapper.setProduct(detail.getProduct());
                wrapper.setShopNo(shopAuthorization.getSn());
                wrapper.setType(type);
                wrappers.add(wrapper);
              }
            }

            iPage.setRecords(wrappers);
            iPage.setTotal(productList.getTotalNum() != null ? productList.getTotalNum() : 0);

            // 记录next_key信息，但在当前架构下无法很好地传递给下一次调用
            if (productList.getNextKey() != null) {
              log.info("获取到next_key: {}，但在当前分页架构下无法自动使用", productList.getNextKey());
            }

            return iPage;

          } catch (Exception e) {
            log.error("[视频号]获取商品列表失败，店铺={}, 类型={}", shopAuthorization.getSn(), type, e);
            iPage.setRecords(Collections.emptyList());
            iPage.setTotal(0);
            return iPage;
          }
        },
        30); // 微信API限制最大30条
  }

  @Override
  public ItemProcessor<ProductItemWrapper, List<PlatformSkuSyncInfo>> getItemProcess() {
    return this::productToPlatformItemSkuInventoryParams;
  }

  @Override
  public ItemWriter<List<PlatformSkuSyncInfo>> getItemWriter() {
    return platformParams -> {
      log.info("[视频号商品同步][ItemWriter]开始处理批次数据，批次大小: {}", platformParams.size());

      int totalProcessed = 0;
      int totalSuccessful = 0;
      int totalFailed = 0;
      int totalDuplicate = 0;

      for (List<PlatformSkuSyncInfo> platformParamDatums : platformParams) {
        if (CollUtil.isEmpty(platformParamDatums)) {
          continue;
        }

        // 按商品ID分组记录
        Map<String, List<PlatformSkuSyncInfo>> groupedByItem =
            platformParamDatums.stream()
                .collect(Collectors.groupingBy(PlatformSkuSyncInfo::getOuterItemId));

        for (Map.Entry<String, List<PlatformSkuSyncInfo>> entry : groupedByItem.entrySet()) {
          String itemId = entry.getKey();
          List<PlatformSkuSyncInfo> skuInfos = entry.getValue();

          try {

            // 调用保存方法
            saveSkuSyncInfo(skuInfos);

            totalSuccessful++;
            log.info(
                "[视频号商品同步][商品成功]✅ 商品转换并持久化成功，ID: {}, 商品名称: {}",
                itemId,
                skuInfos.get(0).getGoodsName());

          } catch (Exception e) {
            totalFailed++;
            log.error(
                "[视频号商品同步][商品失败]❌ 商品转换或持久化失败，ID: {}, production: {}, 错误: {}",
                itemId,
                JsonUtil.toJson(skuInfos),
                e.getMessage(),
                e);
          }

          totalProcessed++;
        }
      }

      log.info(
          "[视频号商品同步][ItemWriter]批次处理完成 - 总处理: {}, 成功: {}, 失败: {}, 重复跳过: {}",
          totalProcessed,
          totalSuccessful,
          totalFailed,
          totalDuplicate);
    };
  }

  /** 将微信商品转换为平台同步信息 */
  List<PlatformSkuSyncInfo> productToPlatformItemSkuInventoryParams(ProductItemWrapper wrapper) {
    final ArrayList<PlatformSkuSyncInfo> paramsList = new ArrayList<>();
    final WeChatProduct product = wrapper.getProduct();

    if (product == null) {
      log.warn("[视频号商品转换]商品为空，跳过转换，店铺: {}, 类型: {}", wrapper.getShopNo(), wrapper.getType());
      return paramsList;
    }

    if (CollUtil.isEmpty(product.getSkus())) {
      log.warn(
          "[视频号商品转换]商品SKU列表为空，跳过转换，商品ID: {}, 商品名称: {}, 店铺: {}",
          product.getProductId(),
          product.getTitle(),
          wrapper.getShopNo());
      return paramsList;
    }

    if (product.getStatus() != 5) {
      log.warn(
          "[视频号商品转换]只处理上架状态的商品，跳过转换，商品ID: {}, 状态: {}",
          product.getProductId(),
          wrapper.getProduct().getStatus());
      return paramsList;
    }

    log.info(
        "[视频号商品转换]开始转换商品，ID: {}, 标题: {}, SKU数量: {}, 状态: {}, 类型: {}, 店铺: {}",
        product.getProductId(),
        product.getTitle(),
        product.getSkus().size(),
        product.getStatus(),
        wrapper.getType(),
        wrapper.getShopNo());

    final int skuNum = product.getSkus().size();

    for (Sku sku : product.getSkus()) {
      try {
        PlatformSkuSyncInfo platformSkuSyncInfo = new PlatformSkuSyncInfo();

        // 平台信息
        platformSkuSyncInfo.setPlatform(defaultType());
        platformSkuSyncInfo.setShopNo(wrapper.getShopNo());

        // SKU基本信息
        platformSkuSyncInfo.setOuterSkuId(sku.getSkuId());
        platformSkuSyncInfo.setOuterItemId(product.getProductId());
        platformSkuSyncInfo.setOuterSkuCode(sku.getSkuCode());
        platformSkuSyncInfo.setOuterItemCode(product.getSpuCode());

        // 库存和价格
        platformSkuSyncInfo.setStock(
            sku.getStockNum() != null ? sku.getStockNum().longValue() : 0L);
        if (sku.getSalePrice() != null) {
          // 微信价格单位是分，转换为元
          platformSkuSyncInfo.setPrice(
              BigDecimal.valueOf(sku.getSalePrice())
                  .divide(BigDecimal.valueOf(100), 2, RoundingMode.HALF_UP));
        }

        // 状态转换：微信状态 5-上架，6-下架 -> 平台状态 1-上架，0-下架
        platformSkuSyncInfo.setStatus(
            product.getStatus() != null && product.getStatus() == 5 ? 1 : 0);

        // 时间信息
        platformSkuSyncInfo.setItemCreateTime(null); // 微信API未提供
        platformSkuSyncInfo.setItemUpdateTime(product.getEditTime());
        platformSkuSyncInfo.setSkuCreateTime(null); // 微信API未提供
        platformSkuSyncInfo.setSkuUpdateTime(product.getEditTime());

        // 商品信息
        platformSkuSyncInfo.setGoodsName(product.getTitle());

        // 规格名称：从SKU属性中提取
        if (CollUtil.isNotEmpty(sku.getSkuAttrs())) {
          String specName =
              sku.getSkuAttrs().stream()
                  .map(Attribute::getAttrValue)
                  .filter(StrUtil::isNotBlank)
                  .collect(Collectors.joining("|"));
          platformSkuSyncInfo.setSpecName(specName);
        }

        platformSkuSyncInfo.setSkuNum(skuNum);

        paramsList.add(platformSkuSyncInfo);

        log.debug(
            "[视频号商品转换][SKU转换]成功转换SKU，商品ID: {}, SKU_ID: {}, SKU_CODE: {}, 库存: {}, 价格: {}分->{}元",
            product.getProductId(),
            sku.getSkuId(),
            sku.getSkuCode(),
            sku.getStockNum(),
            sku.getSalePrice(),
            platformSkuSyncInfo.getPrice());

      } catch (Exception e) {
        log.error(
            "[视频号商品转换][SKU转换失败]转换SKU失败，商品ID: {}, SKU_ID: {}, 错误: {}",
            product.getProductId(),
            sku.getSkuId(),
            e.getMessage(),
            e);
        // 继续处理其他SKU，不中断整个转换过程
      }
    }

    log.info(
        "[视频号商品转换]商品转换完成，ID: {}, 商品名称: {}, 原始SKU数: {}, 成功转换SKU数: {}",
        product.getProductId(),
        product.getTitle(),
        product.getSkus().size(),
        paramsList.size());

    return paramsList;
  }

  @Override
  protected List<PlatformSkuSyncInfo> getSkuSyncInfos(String shopNo, String itemId) {
    try {
      // 根据店铺编号确定类型（这里需要根据实际业务逻辑来确定）
      String type = determineTypeByShopNo(shopNo);

      // 获取商品详情
      WechatProductDetailDto productDetail =
          wechatProductSyncService.getProductDetail(type, itemId);

      if (productDetail == null || productDetail.getProduct() == null) {
        return Collections.emptyList();
      }

      ProductItemWrapper wrapper = new ProductItemWrapper();
      wrapper.setProduct(productDetail.getProduct());
      wrapper.setShopNo(shopNo);
      wrapper.setType(type);

      return productToPlatformItemSkuInventoryParams(wrapper);

    } catch (Exception e) {
      log.error("[视频号]获取SKU同步信息失败，店铺={}, 商品ID={}", shopNo, itemId, e);
      return Collections.emptyList();
    }
  }

  /** 根据店铺编号确定微信类型 TODO: 根据实际业务逻辑实现 */
  private String determineTypeByShopNo(String shopNo) {
    // 这里需要根据实际的业务逻辑来确定
    // 可能需要查询数据库或配置来确定店铺对应的微信类型
    if (shopNo != null && shopNo.contains("member")) {
      return "member";
    }
    return "daddylab";
  }

  @Override
  public void incrementSync(LocalDateTime startTime, LocalDateTime endTime)
      throws UnsupportedOperationException {
    // 微信小店API不支持按时间范围增量同步，可以实现为定期全量同步
    log.info("[视频号]开始增量同步，时间范围: {} - {}", startTime, endTime);

    final List<ShopAuthorization> shopAuthorizations =
        shopAuthorizationService.listNotExpiredAuthorizations(Platform.WECHAT_VIDEO);

    for (ShopAuthorization shopAuthorization : shopAuthorizations) {
      String[] types = {"daddylab", "member"};

      for (String type : types) {
        try {
          // 执行增量同步（实际上是分批次的全量同步）
          run(
              buildStep(
                  getFullDoseItemReader(shopAuthorization, type),
                  platformItemSyncConfig.getChunkSize(),
                  "increment-" + shopAuthorization.getSn() + "-" + type));

        } catch (Exception e) {
          log.error("[视频号]增量同步失败，店铺={}, 类型={}", shopAuthorization.getSn(), type, e);
        }
      }
    }
  }

  @Data
  public static class ProductItemWrapper {
    private WeChatProduct product;
    private String shopNo;
    private String type; // daddylab 或 member
  }

  @Data
  public static class SpecificationInfo {
    private String name;

    @JsonProperty("is_range")
    private Boolean isRange;

    @JsonProperty("value_list")
    private List<ValueInfo> valueList;
  }

  @Data
  public static class ValueInfo {
    private String key;
    private String value;
    private String left; // 范围值使用
    private String right; // 范围值使用
  }

  void saveSkuSyncInfo(List<PlatformSkuSyncInfo> skuSyncInfos) {
    if (CollUtil.isEmpty(skuSyncInfos)) {
      return;
    }

    // 按唯一键去重：shop_no + outer_item_id + outer_sku_id
    Map<String, PlatformSkuSyncInfo> uniqueSkuMap = new LinkedHashMap<>();
    for (PlatformSkuSyncInfo skuInfo : skuSyncInfos) {
      String uniqueKey =
          String.format(
              "%s-%s-%s", skuInfo.getShopNo(), skuInfo.getOuterItemId(), skuInfo.getOuterSkuId());

      if (uniqueSkuMap.containsKey(uniqueKey)) {
        log.warn("[视频号商品同步][去重]检测到批次内重复SKU，保留第一个，KEY: {}", uniqueKey);
      } else {
        uniqueSkuMap.put(uniqueKey, skuInfo);
      }
    }

    List<PlatformSkuSyncInfo> deduplicatedList = new ArrayList<>(uniqueSkuMap.values());
    log.info("[视频号商品同步][去重]原始SKU数量: {}, 去重后数量: {}", skuSyncInfos.size(), deduplicatedList.size());

    savePlatformItemSkus(deduplicatedList);
  }
}
