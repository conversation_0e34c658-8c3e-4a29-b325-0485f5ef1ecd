CREATE TABLE `data_fetch`
(
    `id`          BIGINT AUTO_INCREMENT COMMENT 'id' PRIMARY KEY,
    `data_type`   INT                   DEFAULT 0 COMMENT '数据类型 1:旺店平台商品数据同步 2:系统平台商品 3:...',
    `fetch_point` DATETIME COMMENT '同步时间点（当前记录对应的同步时间范围是从当前时间点到上一个时间点）',
    `lock_time`   DATETIME COMMENT '当前同步任务开始执行时添加独占锁，锁定当前记录直到超时，超时后锁定失效可以被重试',
    `status`      TINYINT      NOT NULL DEFAULT 0 COMMENT '状态 0:未同步完成 1:同步完成 2:同步异常（多次同步失败）',
    `err`         TEXT COMMENT '异常原因',
    `data`        TEXT COMMENT '拓展字段，额外数据（执行过程中认为有必要记录的运行信息）',
    `created_at`  BIGINT       NOT NULL DEFAULT 0 COMMENT '创建时间createAt',
    `created_uid` BIGINT       NOT NULL DEFAULT 0 COMMENT '创建人updateUser',
    `updated_at`  BIGINT       NOT NULL DEFAULT 0 COMMENT '更新时间updateAt',
    `updated_uid` BIGINT       NOT NULL DEFAULT 0 COMMENT '更新人updateUser',
    `is_del`      TINYINT      NOT NULL DEFAULT 0 COMMENT '是否已删除',
    `round`       INT NOT NULL DEFAULT 0 COMMENT '当前拉取周期',
    UNIQUE (`data_type`, `round`, `fetch_point`)
);
