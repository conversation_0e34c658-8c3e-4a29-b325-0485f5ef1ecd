import java.util.LinkedList;
import java.util.concurrent.CountDownLatch;
import java.util.function.Consumer;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.reactivestreams.Subscriber;
import org.reactivestreams.Subscription;
import reactor.core.publisher.Flux;
import reactor.core.publisher.FluxSink;

/**
 * <AUTHOR>
 * @since 2024/12/26
 */
@Slf4j
public class ReactorTests {

  @Test
  public void test() throws InterruptedException {
    final Flux<Integer> flux =
        Flux.create(
            (Consumer<FluxSink<Integer>>)
                fluxSink -> {
                  final LinkedList<Integer> integers = new LinkedList<>();
                  for (int i = 0; i < 100; i++) {
                    integers.add(i + 1);
                  }
                  fluxSink.onRequest(
                      n -> {
                        for (int j = 0; j < n; j++) {
                          final Integer value = integers.poll();
                          if (value != null) {
                            log.info("出>>>>>>>>:{}", value);
                            fluxSink.next(value);
                            log.info("出<<<<<<<<:{}", value);
                          } else {
                            fluxSink.complete();
                            log.info("完成");
                          }
                        }
                      });
                });

    final CountDownLatch countDownLatch = new CountDownLatch(1);
    final Subscriber<Integer> sub =
        new Subscriber<Integer>() {
          private Subscription s;

          @Override
          public void onSubscribe(Subscription s) {
            this.s = s;
            s.request(1);
          }

          @Override
          public void onNext(Integer integer) {
            log.info("处理:{}", integer);
            log.info("请求>>>>>>>");
            s.request(10);
            log.info("请求<<<<<<<");
          }

          @Override
          public void onError(Throwable t) {
            log.error("错误", t);
          }

          @Override
          public void onComplete() {
            log.info("订阅完成||||||||||||||");
            countDownLatch.countDown();
          }
        };
    //    final Flux<Integer> autoConnect =
    //        flux.publishOn(Schedulers.elastic(), 1).publish().autoConnect();
    //    autoConnect.subscribe(sub);
    flux.subscribe(sub);
    countDownLatch.await();
    log.info("end");
  }
}
