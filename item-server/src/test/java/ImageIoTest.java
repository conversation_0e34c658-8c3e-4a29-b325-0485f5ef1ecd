import cn.hutool.core.io.FastByteArrayOutputStream;
import cn.hutool.core.io.FileUtil;
import com.daddylab.supplier.item.infrastructure.cv.CVUtil;
import com.daddylab.supplier.item.infrastructure.cv.ComputerVisionConfig;
import com.daddylab.supplier.item.infrastructure.utils.NumberUtil;
import com.google.common.collect.Lists;
import com.microsoft.azure.cognitiveservices.vision.computervision.ComputerVisionClient;
import java.awt.Graphics2D;
import java.awt.image.BufferedImage;
import java.io.File;
import java.io.IOException;
import java.util.List;
import java.util.function.Function;
import javax.imageio.ImageIO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Pair;
import org.junit.jupiter.api.Test;

/**
 * <AUTHOR>
 * @since 2022/12/1
 */
@Slf4j
public class ImageIoTest {

    @Test
    public void readImage() throws IOException {
        final String imagePath = "/Users/<USER>/Downloads/红心火龙果详情页-1661243964655-1668479381478.jpg";
        final BufferedImage image = ImageIO.read(
                FileUtil.getInputStream(imagePath));
        final int width = image.getWidth();
        final int height = image.getHeight();
        List<Pair<Integer, Integer>> lines = Lists.newLinkedList();
        L1:
        for (int hi = 0; hi < height; hi++) {
            int rgb0 = -1;
            for (int wi = 0; wi < width; wi++) {
                final int rgb = image.getRGB(wi, hi);
                if (wi == 0) {
                    rgb0 = rgb;
                } else if (rgb0 != rgb) {
                    continue L1;
                }
            }
            final Pair<Integer, Integer> lastLine =
                    lines.size() > 0 ? lines.get(lines.size() - 1) : null;
            if (lastLine != null && lastLine.getRight() + 1 == hi) {
                lines.set(lines.size() - 1, Pair.of(lastLine.getLeft(), hi));
            } else {
                lines.add(Pair.of(hi, hi));
            }
        }

        lines.forEach(System.out::println);

        final int numCutPoint = (int) Math.floor(height / 10000f);
        List<Pair<Integer, Integer>> cutLines = Lists.newLinkedList();
        for (int i = 0; i < numCutPoint; i++) {
            int maxY = 10000 * (i + 1);
            for (int j = 0; j < lines.size(); j++) {
                final Pair<Integer, Integer> range = lines.get(j);
                if (range.getLeft() > maxY) {
                    final Pair<Integer, Integer> prevRange = lines.get(j - 1);
                    if (prevRange.getRight() > maxY) {
                        cutLines.add(Pair.of(prevRange.getLeft(), maxY));
                    } else {
                        cutLines.add(prevRange);
                    }
                    break;
                }
            }
        }

        System.out.println("------- cutLines: ---------");
        cutLines.forEach(System.out::println);

        Function<Pair<Integer, Integer>, Integer> fAvgY = range -> NumberUtil.div(
                NumberUtil.add(range.getLeft(), range.getRight()), 2).intValue();

        final String dirPath = "/Users/<USER>/Desktop/test/";
        final File dir = FileUtil.mkdir(dirPath);
        FileUtil.clean(dir);

        cutLines.add(Pair.of(height, height));

        final ComputerVisionConfig computerVisionConfig = new ComputerVisionConfig();
        computerVisionConfig.setSubscriptionKey("78df0f545e2d4322a514c1a306245ce6");
        computerVisionConfig.setEndpoint("https://daddylab-test.cognitiveservices.azure.cn/");
        final ComputerVisionClient computerVisionClient = computerVisionConfig.computerVisionClient();

        for (int i = 0; i < cutLines.size(); i++) {
            int y0 = i > 0 ? fAvgY.apply(cutLines.get(i - 1)) : 0;
            final Integer y1 = fAvgY.apply(cutLines.get(i));
            final int h = y1 - y0;
            log.info("y0 = {}, y1 = {} ,h = {}", y0, y1, h);
            final BufferedImage bufferedImage = new BufferedImage(image.getWidth(), h,
                    image.getType());
            final Graphics2D graphics = bufferedImage.createGraphics();
            graphics.drawImage(image, 0, 0, image.getWidth(), h, 0, y0, image.getWidth(), y1,
                    null);
            ImageIO.write(bufferedImage, "jpg",
                    new File(dirPath + i + ".jpg"));
            try (FastByteArrayOutputStream os = new FastByteArrayOutputStream()) {
                ImageIO.write(bufferedImage, "jpg", os);
                System.out.println("----------- image" + i);
                CVUtil.imageVision(computerVisionClient, os.toByteArray()).toBlocking().value().analyzeResult()
                        .readResults()
                        .forEach(readResult -> readResult.lines()
                                .forEach(line -> System.out.println(line.boundingBox() + ":" + line.text())));
            }
        }

    }
}
