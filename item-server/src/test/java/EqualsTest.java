import com.daddylab.supplier.item.types.inventoryMonitor.IInventoryMonitorId;
import com.daddylab.supplier.item.types.inventoryMonitor.InventoryMonitorId;
import com.daddylab.supplier.item.types.inventoryMonitor.SetMonitorThresholdCmd;
import org.junit.jupiter.api.Test;

import java.util.HashMap;

/**
 * <AUTHOR>
 * @since 2024/4/3
 */
public class EqualsTest {
    @Test
    public void setUniqueTest() {

        final InventoryMonitorId inventoryMonitorId = new InventoryMonitorId();
        inventoryMonitorId.setWarehouseNo("123");
        inventoryMonitorId.setSkuNo("");


        final SetMonitorThresholdCmd setMonitorThresholdCmd = new SetMonitorThresholdCmd();
        setMonitorThresholdCmd.setAlertThreshold(2);
        setMonitorThresholdCmd.setWarehouseNo("123");
        setMonitorThresholdCmd.setSkuNo(null);

        final HashMap<IInventoryMonitorId, String> map = new HashMap<>();
        map.put(inventoryMonitorId, "123");

        System.out.println(map.get(setMonitorThresholdCmd));


    }
}
