import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONWriter;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ItemDrawer;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

/**
 * <AUTHOR>
 * @since 2024/7/11
 */
public class FastJSONTests {

    @Test
    public void testJsonSerializationInclude() {
        final ItemDrawer itemDrawer = new ItemDrawer();
        itemDrawer.setId(1L);
        itemDrawer.setTbId("123123");
        itemDrawer.setTbLink("123");
        final String jsonString = JSON.toJSONString(itemDrawer, JSONWriter.Feature.WriteNulls);
        Assertions.assertTrue(jsonString.contains("null"), "jsonString should contain null");
    }
}
