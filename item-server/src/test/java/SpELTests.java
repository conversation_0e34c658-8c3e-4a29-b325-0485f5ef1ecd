import org.junit.jupiter.api.Test;
import org.springframework.expression.Expression;
import org.springframework.expression.ExpressionParser;
import org.springframework.expression.spel.standard.SpelExpressionParser;

/**
 * <AUTHOR>
 * @since 2023/9/20
 */
public class SpELTests {

    @Test
    public void getValueFromCommaSeparatorString() {
        String str = "apple,banana,orange";
        ExpressionParser parser = new SpelExpressionParser();
        Expression exp = parser.parseExpression("#root.split(',')[0]");
        String firstValue = (String) exp.getValue(str);
        System.out.println("第一个值： " + firstValue);
    }
}
