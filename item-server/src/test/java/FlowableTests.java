import cn.hutool.core.collection.ListUtil;
import com.alibaba.fastjson.JSON;
import com.daddylab.supplier.item.application.offShelf.OffShelfProcessListener;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import org.flowable.bpmn.model.Process;
import org.flowable.bpmn.model.*;
import org.flowable.common.engine.impl.persistence.StrongUuidGenerator;
import org.flowable.common.rest.util.RestUrlBuilder;
import org.flowable.engine.*;
import org.flowable.engine.impl.cfg.ProcessEngineConfigurationImpl;
import org.flowable.engine.impl.cfg.StandaloneProcessEngineConfiguration;
import org.flowable.engine.repository.ProcessDefinition;
import org.flowable.engine.runtime.Execution;
import org.flowable.engine.runtime.ProcessInstance;
import org.flowable.rest.service.api.RestResponseFactory;
import org.flowable.task.api.Task;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

import java.io.IOException;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/9/28
 */
@Slf4j
public class FlowableTests {

    @Test
    public void processDefJson() throws IOException {
        ProcessEngine processEngine = getProcessEngine();
        final RepositoryService repositoryService = processEngine.getRepositoryService();
        final String processDefKey = "item_optimize_0.0";
        final List<ProcessDefinition> processDefinitions =
                repositoryService
                        .createProcessDefinitionQuery()
                        .processDefinitionKey(processDefKey)
                        .list();
        for (ProcessDefinition processDefinition : processDefinitions) {
            final BpmnModel bpmnModel = repositoryService.getBpmnModel(processDefinition.getId());
            final Process process = bpmnModel.getMainProcess();
            final FlowElement initialFlowElement = process.getInitialFlowElement();
            log.info("流程启动 流程定义ID={}", processDefinition.getId());

            if (!(initialFlowElement instanceof FlowNode)) {
                log.error("初始节点类型错误:" + initialFlowElement.getClass().getSimpleName());
                break;
            }
            final FlowNode initialFlowNode = (FlowNode) initialFlowElement;
            flowContinue(initialFlowNode);
            log.info("流程结束");
        }
    }

    private static void flowContinue(FlowNode initialFlowNode) {
        log.info(
                "活动开始 {}",
                Arrays.toString(
                        new Object[]{
                                initialFlowNode.getId(),
                                initialFlowNode.getName(),
                                initialFlowNode.getClass().getSimpleName()
                        }));
        for (SequenceFlow outgoingFlow : initialFlowNode.getOutgoingFlows()) {
            final FlowElement targetFlowElement = outgoingFlow.getTargetFlowElement();
            log.info(
                    "活动流转 {}",
                    Arrays.toString(
                            new Object[]{
                                    " -> ",
                                    targetFlowElement.getId(),
                                    targetFlowElement.getName(),
                                    targetFlowElement.getClass().getSimpleName()
                            }));
            if (targetFlowElement instanceof FlowNode) {
                flowContinue(((FlowNode) targetFlowElement));
            }
        }
    }

    @Test
    public void helloFlowable() throws IOException {
        ProcessEngine processEngine = getProcessEngine();
        final RepositoryService repositoryService = processEngine.getRepositoryService();
        final String processDefKey = "item_optimize_0.0";
        final List<ProcessDefinition> processDefinitions =
                repositoryService
                        .createProcessDefinitionQuery()
                        .processDefinitionKey(processDefKey)
                        .list();
        final ProcessDefinition testProcessDefinition;
        if (processDefinitions.isEmpty()) {
            repositoryService
                    .createDeployment()
                    .addClasspathResource("item_optimize_01.bpmn20.xml")
                    .key(processDefKey)
                    .deploy();
            testProcessDefinition =
                    repositoryService
                            .createProcessDefinitionQuery()
                            .processDefinitionKey(processDefKey)
                            .singleResult();
        } else {
            testProcessDefinition = processDefinitions.get(0);
        }

        if (processDefinitions.size() > 1) {
            for (int i = 1; i < processDefinitions.size(); i++) {
                final String deploymentId = processDefinitions.get(i).getDeploymentId();
                repositoryService.deleteDeployment(deploymentId);
                System.out.println("delete deployment: " + deploymentId);
            }
        }

        final RestResponseFactory restResponseFactory = getRestResponseFactory();

        final String id = testProcessDefinition.getId();
        System.out.println("processDefId=" + id);

        final ProcessInstance processInstance =
                processEngine
                        .getRuntimeService()
                        .createProcessInstanceBuilder()
                        .processDefinitionId(testProcessDefinition.getId())
                        .businessKey("test_item_optimize_001")
                        .variable("qc_candidates", "chengzu.wu,chengwei.wu")
                        .variable("legal_candidates", "chenghong.song")
                        .variable("modifier_candidates", "dixiang.liu")
                        .start();
        System.out.println(
                "processInstance="
                        + restResponseFactory.createProcessInstanceResponse(processInstance));
        final List<Execution> executionList =
                processEngine.getRuntimeService().createExecutionQuery().list();

        final List<Task> taskList = processEngine.getTaskService().createTaskQuery().list();
        System.out.println(
                "taskList="
                        + JSON.toJSONString(restResponseFactory.createTaskResponseList(taskList)));
        //        for (Task task : taskList) {
        //            processEngine.getTaskService().complete(task.getId());
        //        }
    }

    @NonNull
    private static RestResponseFactory getRestResponseFactory() {
        final ObjectMapper objectMapper = new ObjectMapper();
        final RestUrlBuilder restUrlBuilder = RestUrlBuilder.usingBaseUrl("/");
        final RestResponseFactory restResponseFactory =
                new RestResponseFactory(objectMapper) {
                    @Override
                    protected RestUrlBuilder createUrlBuilder() {
                        return restUrlBuilder;
                    }
                };
        return restResponseFactory;
    }

    @Test
    public void completeTaskByNonAssignee() {
        final ProcessEngine processEngine = getProcessEngine();
        final TaskService taskService = processEngine.getTaskService();
        final IdentityService identityService = processEngine.getIdentityService();
        final RestResponseFactory restResponseFactory = getRestResponseFactory();
        final List<Task> taskList =
                taskService.createTaskQuery().taskCandidateOrAssigned("chenghong.song").list();
        log.info("taskList={}", restResponseFactory.createTaskResponseList(taskList));
        identityService.setAuthenticatedUserId("wuwu66");
        for (Task task : taskList) {
            taskService.complete(task.getId());
        }
    }

    @NonNull
    private static ProcessEngine getProcessEngine() {
        final ProcessEngineConfigurationImpl cfg = new StandaloneProcessEngineConfiguration();
        cfg.setJdbcUrl("****************************************************")
           .setJdbcUsername("root")
           .setJdbcPassword("P@ssw0rd")
           .setJdbcDriver("com.mysql.cj.jdbc.Driver")
           .setDatabaseSchemaUpdate(ProcessEngineConfiguration.DB_SCHEMA_UPDATE_TRUE);
        cfg.setIdmEngineConfigurator(null).setEventRegistryConfigurator(null);
        cfg.setIdGenerator(new StrongUuidGenerator());
        cfg.setEnableDatabaseEventLogging(true);
        cfg.setEnableHistoricTaskLogging(true);
        final ProcessEngine processEngine = cfg.buildProcessEngine();
        Assertions.assertNotNull(processEngine);
        return processEngine;
    }


    @Test
    public void offsetShelfProcess() throws IOException {
        ProcessEngine processEngine = getProcessEngine();
        final OffShelfProcessListener listenerToAdd = new OffShelfProcessListener();
        processEngine.getRuntimeService().addEventListener(listenerToAdd);
        final RepositoryService repositoryService = processEngine.getRepositoryService();
        repositoryService.createDeploymentQuery()
                         .list()
                         .forEach(deployment -> {
                             for (ProcessInstance processInstance : processEngine.getRuntimeService()
                                                                                 .createProcessInstanceQuery()
                                                                                 .list()) {
                                 processEngine.getRuntimeService()
                                              .deleteProcessInstance(processInstance.getId(), "delete");
                             }
                             repositoryService.deleteDeployment(deployment.getId());
                         });
        final String processDefKey = "offShelf";
        repositoryService
                .createDeployment()
                .addClasspathResource("off_shelf.bpmn20.xml")
                .key(processDefKey)
                .deploy();

        final RestResponseFactory restResponseFactory = getRestResponseFactory();
        final ProcessInstance processInstance =
                processEngine
                        .getRuntimeService()
                        .createProcessInstanceBuilder()
                        .processDefinitionKey(processDefKey)
                        .businessKey("offShelf:1")
                        .start();
        System.out.println(
                "processInstance="
                        + restResponseFactory.createProcessInstanceResponse(processInstance));

        while (true) {
            final List<Task> taskList = processEngine.getTaskService()
                                                     .createTaskQuery()
                                                     .processInstanceId(processInstance.getProcessInstanceId())
                                                     .list();
            if (taskList.isEmpty()) {
                log.info("finish");
                break;
            }
            log.info("task:{}", restResponseFactory.createTaskResponseList(taskList));
            for (Task task : taskList) {
                if (task.getName().equals("待审核")) {
                    final HashMap<String, Object> variables = new HashMap<>();
                    variables.put("approval", "agree");
                    variables.put("processBindingIds", ListUtil.of(1L, 2L, 3L));

                    processEngine.getTaskService().complete(task.getId(), variables);
                } else {
                    processEngine.getTaskService().complete(task.getId());
                }
            }
        }
    }
}
