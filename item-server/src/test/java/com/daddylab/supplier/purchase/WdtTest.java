package com.daddylab.supplier.purchase;

import com.daddylab.supplier.item.ItemApplication;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.WdtOrderMapper;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * <AUTHOR> up
 * @date 2022/4/18 6:44 下午
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = ItemApplication.class)
@ActiveProfiles("local")
public class WdtTest {

    @Autowired
    WdtOrderMapper wdtOrderMapper;

    @Test
    public void test() {
//        LocalDateTime operate = DateUtil.parse("202204", "yyyyMM");
//        String startDt = DateUtil.getFirstDayOfMonth(operate).format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:dd"));
//        String endDt = DateUtil.getFirstDayOfMonth(operate.plusMonths(1)).format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:dd"));
//        List<WdtOrderDetailDO> wdtOrderDetailDOList = wdtOrderMapper.orderDetailList(startDt, endDt, 0, 100);
    }


}
