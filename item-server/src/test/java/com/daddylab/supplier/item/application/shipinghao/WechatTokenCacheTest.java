package com.daddylab.supplier.item.application.shipinghao;

import com.daddylab.supplier.item.application.shipinghao.dto.WechatAccessTokenDto;
import com.daddylab.supplier.item.application.shipinghao.service.WechatApiService;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.data.redis.core.StringRedisTemplate;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 微信Token缓存测试类
 * 
 * <AUTHOR>
 * @date 2024-12-19
 */
@Slf4j
@SpringBootTest
public class WechatTokenCacheTest {
    
    @Autowired
    private WechatApiService wechatApiService;
    
    @Autowired
    private StringRedisTemplate redisTemplate;
    
    private static final String ACCESS_TOKEN_CACHE_KEY = "wechat:access_token";
    
    /**
     * 测试Token缓存功能
     */
    @Test
    public void testTokenCache(String type) {
        log.info("开始测试Token缓存功能");
        
        // 1. 清除现有缓存
        wechatApiService.clearCachedAccessToken(type);
        log.info("已清除现有缓存");
        
        // 2. 验证缓存已被清除
        String cachedToken = redisTemplate.opsForValue().get(ACCESS_TOKEN_CACHE_KEY);
        assertNull(cachedToken, "缓存应该已被清除");
        log.info("验证缓存已被清除");
        
        // 3. 第一次获取Token（应该调用API）
        log.info("第一次获取Token（应该调用API）");
        WechatAccessTokenDto token1 = wechatApiService.getAccessToken(type);
        assertNotNull(token1, "第一次获取Token不应该为null");
        log.info("第一次获取Token成功: {}", token1.getAccessToken());
        
        // 4. 验证Token已缓存到Redis
        cachedToken = redisTemplate.opsForValue().get(ACCESS_TOKEN_CACHE_KEY);
        assertNotNull(cachedToken, "Token应该已缓存到Redis");
        assertEquals(token1.getAccessToken(), cachedToken, "缓存的Token应该与返回的Token一致");
        log.info("验证Token已缓存到Redis");
        
        // 5. 第二次获取Token（应该从缓存获取）
        log.info("第二次获取Token（应该从缓存获取）");
        WechatAccessTokenDto token2 = wechatApiService.getAccessToken(type);
        assertNotNull(token2, "第二次获取Token不应该为null");
        assertEquals(token1.getAccessToken(), token2.getAccessToken(), "两次获取的Token应该一致");
        log.info("第二次获取Token成功（从缓存）: {}", token2.getAccessToken());
        
        // 6. 测试清除缓存功能
        log.info("测试清除缓存功能");
        wechatApiService.clearCachedAccessToken(type);
        cachedToken = redisTemplate.opsForValue().get(ACCESS_TOKEN_CACHE_KEY);
        assertNull(cachedToken, "缓存应该已被清除");
        log.info("验证缓存清除功能正常");
        
        log.info("Token缓存功能测试完成");
    }
    
    /**
     * 测试Token缓存过期时间
     */
    @Test
    public void testTokenCacheExpiration(String type) {
        log.info("开始测试Token缓存过期时间");
        
        // 1. 清除现有缓存
        wechatApiService.clearCachedAccessToken(type);
        
        // 2. 获取Token并缓存
        WechatAccessTokenDto token = wechatApiService.getAccessToken(type);
        assertNotNull(token, "获取Token不应该为null");
        log.info("获取Token并缓存: {}", token.getAccessToken());
        
        // 3. 验证缓存存在
        String cachedToken = redisTemplate.opsForValue().get(ACCESS_TOKEN_CACHE_KEY);
        assertNotNull(cachedToken, "Token应该已缓存");
        
        // 4. 检查过期时间（应该接近7000秒）
        Long ttl = redisTemplate.getExpire(ACCESS_TOKEN_CACHE_KEY);
        assertNotNull(ttl, "应该能获取到过期时间");
        assertTrue(ttl > 6900 && ttl <= 7000, "过期时间应该在6900-7000秒之间，实际: " + ttl);
        log.info("Token缓存过期时间: {}秒", ttl);
        
        log.info("Token缓存过期时间测试完成");
    }
    
    /**
     * 测试连续获取Token的性能
     */
    @Test
    public void testTokenCachePerformance(String type) {
        log.info("开始测试Token缓存性能");
        
        // 1. 清除现有缓存
        wechatApiService.clearCachedAccessToken(type);
        
        // 2. 第一次获取（调用API）
        long startTime = System.currentTimeMillis();
        WechatAccessTokenDto token1 = wechatApiService.getAccessToken(type);
        long firstCallTime = System.currentTimeMillis() - startTime;
        log.info("第一次获取Token耗时: {}ms", firstCallTime);
        
        // 3. 连续多次获取（从缓存）
        long totalCacheTime = 0;
        int cacheCallCount = 10;
        
        for (int i = 0; i < cacheCallCount; i++) {
            startTime = System.currentTimeMillis();
            WechatAccessTokenDto token = wechatApiService.getAccessToken(type);
            long cacheCallTime = System.currentTimeMillis() - startTime;
            totalCacheTime += cacheCallTime;
            
            assertEquals(token1.getAccessToken(), token.getAccessToken(), 
                    "缓存的Token应该与第一次获取的Token一致");
        }
        
        long avgCacheTime = totalCacheTime / cacheCallCount;
        log.info("{}次缓存获取平均耗时: {}ms", cacheCallCount, avgCacheTime);
        
        // 4. 验证缓存获取比API调用快
        assertTrue(avgCacheTime < firstCallTime, 
                "缓存获取应该比API调用快，缓存平均: " + avgCacheTime + "ms, API调用: " + firstCallTime + "ms");
        
        log.info("Token缓存性能测试完成");
    }
} 