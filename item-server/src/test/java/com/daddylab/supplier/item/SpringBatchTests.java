package com.daddylab.supplier.item;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.daddylab.supplier.item.infrastructure.config.batch.reader.mybatisplus.MybatisPlusPagingItemReader;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.batch.core.*;
import org.springframework.batch.core.configuration.annotation.JobBuilderFactory;
import org.springframework.batch.core.configuration.annotation.StepBuilderFactory;
import org.springframework.batch.core.launch.JobLauncher;
import org.springframework.batch.core.launch.support.SimpleJobLauncher;
import org.springframework.batch.core.repository.JobRepository;
import org.springframework.batch.core.repository.support.MapJobRepositoryFactoryBean;
import org.springframework.batch.item.ItemProcessor;
import org.springframework.batch.item.ItemWriter;
import org.springframework.batch.support.transaction.ResourcelessTransactionManager;
import org.springframework.transaction.PlatformTransactionManager;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;

import static org.junit.jupiter.api.Assertions.*;

@Slf4j
public class SpringBatchTests {
    
    @Test
    public void testMybatisPlusPagingItemReader() throws Exception {
        // 1. 设置测试数据
        final int total = 10000;
        final int pageSize = 100;
        
        final AtomicInteger readCount = new AtomicInteger(0);
        AtomicInteger processedCount = new AtomicInteger();
        List<Integer> processedItems = new ArrayList<>();

        // 2. 创建并配置 MybatisPlusPagingItemReader
        MybatisPlusPagingItemReader<Integer> mybatisPlusPagingItemReader = new MybatisPlusPagingItemReader<>();
        mybatisPlusPagingItemReader.setPageSize(pageSize);
        mybatisPlusPagingItemReader.setFunction(new Function<IPage<Integer>, IPage<Integer>>() {
            @Override
            public IPage<Integer> apply(IPage<Integer> integerIPage) {
                log.info("正在读取第 {} 页，页大小: {}", integerIPage.getCurrent(), integerIPage.getSize());

                // 模拟数据库分页查询
                Page<Integer> integerPage = new Page<>(integerIPage.getCurrent(), integerIPage.getSize(), total);
                ArrayList<Integer> records = new ArrayList<>();

                // 如果已经读取完所有数据，返回 null 结束读取
                if (readCount.get() >= total) {
                    log.info("数据读取完毕，总共读取了 {} 条记录", readCount.get());
                    return null;
                }

                // 模拟分页数据生成
                int firstItem = ((int) integerIPage.getCurrent() - 1) * pageSize + 1;
                for (int i = firstItem; i < firstItem + pageSize; i++) {
                    records.add(i);
                    readCount.incrementAndGet();
                }

                integerPage.setRecords(records);
                log.info("第 {} 页读取了 {} 条记录: {}", integerIPage.getCurrent(), records.size(), records);
                return integerPage;
            }
        });

        // 3. 创建 ItemProcessor（可选的数据处理）
        ItemProcessor<Integer, Integer> processor = new ItemProcessor<Integer, Integer>() {
            @Override
            public Integer process(Integer item) throws Exception {
                return item;
            }
        };

        // 4. 创建 ItemWriter（数据写入）
        ItemWriter<Integer> writer = new ItemWriter<Integer>() {
            @Override
            public void write(List<? extends Integer> items) throws Exception {
                processedCount.addAndGet(items.size());
                processedItems.addAll(items);
                log.info("写入了 {} 条记录，当前总处理数量: {} items:{}", items.size(), processedCount.get(), items);
            }
        };

        // 5. 创建 Spring Batch 基础设施
        PlatformTransactionManager transactionManager = new ResourcelessTransactionManager();

        // 创建 JobRepository
        MapJobRepositoryFactoryBean jobRepositoryFactory = new MapJobRepositoryFactoryBean();
        jobRepositoryFactory.setTransactionManager(transactionManager);
        jobRepositoryFactory.afterPropertiesSet();
        JobRepository jobRepository = jobRepositoryFactory.getObject();

        // 创建 JobLauncher
        SimpleJobLauncher jobLauncher = new SimpleJobLauncher();
        jobLauncher.setJobRepository(jobRepository);
        jobLauncher.afterPropertiesSet();

        // 创建 JobBuilderFactory 和 StepBuilderFactory
        JobBuilderFactory jobBuilderFactory = new JobBuilderFactory(jobRepository);
        StepBuilderFactory stepBuilderFactory = new StepBuilderFactory(jobRepository, transactionManager);

        // 6. 创建 Step
        Step step = stepBuilderFactory.get("testStep")
                .<Integer, Integer>chunk(50) // 每次处理50条记录
                .reader(mybatisPlusPagingItemReader)
                .processor(processor)
                .writer(writer)
                .build();

        // 7. 创建 Job
        Job job = jobBuilderFactory.get("testJob")
                .start(step)
                .build();

        // 8. 执行 Job
        JobParameters jobParameters = new JobParametersBuilder()
                .addLong("timestamp", System.currentTimeMillis())
                .toJobParameters();

        JobExecution jobExecution = jobLauncher.run(job, jobParameters);

        // 9. 验证执行结果
        assertEquals(BatchStatus.COMPLETED, jobExecution.getStatus());

        log.info("测试完成！总共处理了 {} 条记录", processedCount.get());
        
        log.info("processedItems:{}", processedItems);
    }
}
