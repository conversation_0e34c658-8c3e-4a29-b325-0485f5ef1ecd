package com.daddylab.supplier.item.application.platformItemSkuInventory.support;

import com.daddylab.supplier.item.ItemApplication;
import com.daddylab.supplier.item.application.shipinghao.dto.WechatProductListDto;
import com.daddylab.supplier.item.application.shipinghao.service.WechatApiService;
import com.daddylab.supplier.item.application.shop.ShopAuthorizationBizService;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import com.daddylab.supplier.item.application.shipinghao.dto.WechatAccessTokenDto;
import com.daddylab.supplier.item.application.shipinghao.dto.WechatProductDetailDto;

/**
 * 视频号商品同步测试类 用于在测试环境启动实际的同步流程
 *
 * <AUTHOR>
 * @date 2024-12-19
 */
@Slf4j
@SpringBootTest(classes = {ItemApplication.class})
public class ShiPingHaoItemSyncTest {

  @Autowired private WechatVideoItemSyncServiceImpl shiPingHaoItemSyncService;

  @Autowired private WechatApiService wechatApiService;

  @Autowired ShopAuthorizationBizService shopAuthorizationBizService;

  /** 测试next_key分页逻辑 验证微信API的游标分页是否正确工作 */
  @Test
  public void testNextKeyPagination() {
    log.info("开始测试next_key分页逻辑");

    try {
      String type = "daddylab";
      int pageSize = 5; // 使用较小的页面大小便于测试
      String nextKey = null;
      int totalFetched = 0;
      int pageCount = 0;

      // 获取访问令牌
      WechatAccessTokenDto accessToken = wechatApiService.getAccessToken(type);
      if (accessToken == null || !accessToken.isSuccess()) {
        log.error("获取访问令牌失败，类型: {}", type);
        return;
      }

      log.info("成功获取访问令牌，类型: {}", type);

      // 分页获取数据，最多获取3页作为测试
      while (pageCount < 3) {
        pageCount++;
        log.info("正在获取第{}页数据，next_key: {}", pageCount, nextKey);

        WechatProductListDto productList =
            wechatApiService.getProductListWithNextKey(
                type, accessToken.getAccessToken(), pageSize, nextKey);

        if (productList == null
            || productList.getProductIds() == null
            || productList.getProductIds().isEmpty()) {
          log.info("第{}页没有更多数据，结束分页", pageCount);
          break;
        }

        int currentPageSize = productList.getProductIds().size();
        totalFetched += currentPageSize;

        log.info(
            "第{}页获取成功，商品数量: {}, 累计获取: {}, 总数: {}",
            pageCount,
            currentPageSize,
            totalFetched,
            productList.getTotalNum());

        // 打印商品ID
        for (String productId : productList.getProductIds()) {
          log.info("  商品ID: {}", productId);
        }

        // 更新next_key
        nextKey = productList.getNextKey();
        log.info("下一页next_key: {}", nextKey);

        // 如果没有next_key，说明已经是最后一页
        if (nextKey == null || nextKey.isEmpty()) {
          log.info("没有next_key，已到达最后一页");
          break;
        }
      }

      log.info("next_key分页测试完成，总共获取{}页，{}个商品", pageCount, totalFetched);

    } catch (Exception e) {
      log.error("next_key分页测试失败", e);
      throw e;
    }
  }

  /** 测试全量同步 启动实际的同步流程，将视频号平台的商品信息拉回来 */
  @Test
  public void testFullSync() {

    shopAuthorizationBizService.authorize("K0002");
    shopAuthorizationBizService.authorize("K0340");

    log.info("开始执行视频号商品全量同步测试");

    try {
      // 启动全量同步
      shiPingHaoItemSyncService.fullDoseSync();

      log.info("视频号商品全量同步调用完成，但批处理作业可能仍在后台运行");
      
      // 等待一段时间让批处理作业完成
      // 注意：这是一个简单的等待方式，实际生产环境建议使用更精确的等待机制
      log.info("等待批处理作业完成...");
      Thread.sleep(60000); // 等待60秒，根据实际数据量调整
      
      log.info("等待完成，测试结束");

    } catch (InterruptedException e) {
      log.error("测试等待被中断", e);
      Thread.currentThread().interrupt();
      throw new RuntimeException("测试被中断", e);
    } catch (Exception e) {
      log.error("视频号商品全量同步执行失败", e);
      throw e;
    }
  }

  /** 
   * 测试全量同步（改进版）
   * 通过监控JobExecution状态来等待作业完成
   */
  @Test
  public void testFullSyncWithMonitoring() {
    shopAuthorizationBizService.authorize("K0002");
    shopAuthorizationBizService.authorize("K0340");

    log.info("开始执行视频号商品全量同步测试（监控版）");

    try {
      // 在单独线程中执行同步
      Thread syncThread = new Thread(() -> {
        try {
          shiPingHaoItemSyncService.fullDoseSync();
        } catch (Exception e) {
          log.error("同步线程执行失败", e);
        }
      });
      
      syncThread.start();
      
      // 等待同步线程完成，最多等待10分钟
      syncThread.join(600000); // 600秒 = 10分钟
      
      if (syncThread.isAlive()) {
        log.warn("同步作业超时，强制停止等待");
        syncThread.interrupt();
      } else {
        log.info("同步作业已完成");
      }

    } catch (InterruptedException e) {
      log.error("测试被中断", e);
      Thread.currentThread().interrupt();
    } catch (Exception e) {
      log.error("视频号商品全量同步测试失败", e);
      throw new RuntimeException("全量同步测试失败", e);
    }
  }

  /** 
   * 测试全量同步（精确监控版）
   * 使用executeFullDoseSync方法监控JobExecution状态
   */
  @Test
  public void testFullSyncWithJobExecution() throws Exception {
    shopAuthorizationBizService.authorize("K0002");
    shopAuthorizationBizService.authorize("K0003");

    log.info("开始执行视频号商品全量同步测试（JobExecution监控版）");

    try {
      // 使用新的方法获取JobExecution
      org.springframework.batch.core.JobExecution jobExecution = 
          shiPingHaoItemSyncService.executeFullDoseSync();
      
      log.info("Job启动成功，JobExecution ID: {}, 状态: {}", 
          jobExecution.getId(), jobExecution.getStatus());
      
      // 等待作业完成
      while (jobExecution.isRunning()) {
        log.info("作业正在运行，当前状态: {}, 已运行时间: {}ms", 
            jobExecution.getStatus(), 
            System.currentTimeMillis() - jobExecution.getStartTime().getTime());
        
        Thread.sleep(5000); // 每5秒检查一次状态
      }
      
      // 作业完成，输出最终状态
      log.info("作业完成！最终状态: {}", jobExecution.getStatus());
      log.info("开始时间: {}", jobExecution.getStartTime());
      log.info("结束时间: {}", jobExecution.getEndTime());
      
      if (jobExecution.getStatus() == org.springframework.batch.core.BatchStatus.COMPLETED) {
        log.info("✅ 全量同步成功完成");
      } else if (jobExecution.getStatus() == org.springframework.batch.core.BatchStatus.FAILED) {
        log.error("❌ 全量同步执行失败");
        jobExecution.getAllFailureExceptions().forEach(ex -> {
          log.error("失败异常: ", ex);
        });
      } else {
        log.warn("⚠️ 全量同步状态异常: {}", jobExecution.getStatus());
      }

    } catch (InterruptedException e) {
      log.error("测试等待被中断", e);
      Thread.currentThread().interrupt();
      throw new RuntimeException("测试被中断", e);
    } catch (Exception e) {
      log.error("视频号商品全量同步测试失败", e);
      throw e;
    }
  }

  /** 测试单个商品同步 同步指定的单个商品 */
  @Test
  public void testSyncSingleItem() {
    log.info("开始执行视频号单个商品同步测试");

    try {
      // 这里需要替换为实际的店铺编号和商品ID
      String shopNo = "test-shop";
      String outerItemId = "10000248893987"; // 替换为实际的商品ID

      // 执行单个商品同步
      shiPingHaoItemSyncService.syncItem(shopNo, outerItemId);

      log.info("视频号单个商品同步执行完成，商品ID: {}", outerItemId);

    } catch (Exception e) {
      log.error("视频号单个商品同步执行失败", e);
      throw e;
    }
  }

  /**
   * 测试JSON反序列化
   * 验证SizeChart等对象的反序列化是否正常
   */
  @Test
  public void testJsonDeserialization() {
    log.info("开始测试JSON反序列化");
    
    try {
      String type = "daddylab";
      
      // 获取访问令牌
      WechatAccessTokenDto accessToken = wechatApiService.getAccessToken(type);
      if (accessToken == null || !accessToken.isSuccess()) {
        log.error("获取访问令牌失败，类型: {}", type);
        return;
      }
      
      // 获取商品列表
      WechatProductListDto productList = wechatApiService.getProductListWithNextKey(
          type, accessToken.getAccessToken(), 5, null);
      
      if (productList != null && productList.getProductIds() != null && !productList.getProductIds().isEmpty()) {
        String productId = productList.getProductIds().get(0);
        log.info("测试商品ID: {}", productId);
        
        // 尝试获取商品详情（这里之前会出现反序列化错误）
        WechatProductDetailDto productDetail = wechatApiService.getProductDetail(
            type, accessToken.getAccessToken(), productId);
        
        if (productDetail != null && productDetail.getProduct() != null) {
          log.info("✅ JSON反序列化成功！商品标题: {}", productDetail.getProduct().getTitle());
          
          // 检查SizeChart字段
          if (productDetail.getProduct().getSizeChart() != null) {
            log.info("尺码表信息: ID={}, 名称={}", 
                productDetail.getProduct().getSizeChart().getSizeChartId(),
                productDetail.getProduct().getSizeChart().getName());
            
            if (productDetail.getProduct().getSizeChart().getSpecificationList() != null) {
              log.info("规格列表数量: {}", 
                  productDetail.getProduct().getSizeChart().getSpecificationList().size());
            }
          } else {
            log.info("该商品没有尺码表信息");
          }
        } else {
          log.error("❌ 获取商品详情失败");
        }
      } else {
        log.warn("没有可测试的商品");
      }
      
    } catch (Exception e) {
      log.error("JSON反序列化测试失败", e);
      throw new RuntimeException("JSON反序列化测试失败", e);
    }
  }
}
