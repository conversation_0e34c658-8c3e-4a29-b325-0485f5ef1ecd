package com.daddylab.supplier.item;

import com.alibaba.cola.dto.PageResponse;
import com.alibaba.cola.dto.SingleResponse;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.daddylab.supplier.item.application.message.MessageBizService;
import com.daddylab.supplier.item.application.message.event.BackItemMessageEvent;
import com.daddylab.supplier.item.application.message.event.PlatformWarnMessageEvent;
import com.daddylab.supplier.item.application.message.event.SysPurchaseOrderEvent;
import com.daddylab.supplier.item.application.message.param.ConfigCmd;
import com.daddylab.supplier.item.application.message.param.ListPageQuery;
import com.daddylab.supplier.item.application.message.vo.ConfigVoList;
import com.daddylab.supplier.item.application.message.vo.MessageVO;
import com.daddylab.supplier.item.controller.message.MessageController;
import com.daddylab.supplier.item.domain.message.dto.MsgFillObj;
import com.daddylab.supplier.item.infrastructure.common.UserContext;
import com.daddylab.supplier.item.infrastructure.config.event.EventBusUtil;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.Message;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.MessageConfig;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.MessagePushType;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IMessageConfigService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IMessageService;
import com.daddylab.supplier.item.infrastructure.utils.JsonUtil;
import com.fasterxml.jackson.core.type.TypeReference;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.junit.Before;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Order;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/12/21 5:53 下午
 * @description
 */
@SpringBootTest(properties = {
        "spring.profiles.active:test"
})
@Slf4j
public class MessageTest {

    @Autowired
    MessageBizService messageBizService;

    @Autowired
    MessageController messageController;

    UserContext.UserInfo userInfo;

    @Before
    @DisplayName("mock用户信息")
    public void getUserInfo() {
        if (Objects.isNull(userInfo)) {
            this.userInfo = new UserContext.UserInfo();
            userInfo.setUserId(10001L);
            userInfo.setUserName("单元测试01号");
        }
    }

    @Test
    @DisplayName("保存消息")
    public void saveConfig(){
        String list = "[{\"canEffect\":true,\"id\":1,\"operationType\":\"ADD_BACK_ITEM\",\"recipients\":[{\"value\":138577,\"label\":\"吉凯11\"},{\"value\":121645,\"label\":\"王大力\"},{\"value\":33,\"label\":\"王泽君\"}],\"pushDefault\":true,\"pushMail\":false,\"pushRemind\":false,\"pushText\":false},{\"canEffect\":true,\"id\":2,\"operationType\":\"EDIT_BACK_ITEM\",\"recipients\":[],\"pushDefault\":true,\"pushMail\":false,\"pushRemind\":false,\"pushText\":false},{\"canEffect\":true,\"id\":3,\"operationType\":\"WARN_PLATFORM_ITEM\",\"recipients\":[],\"pushDefault\":true,\"pushMail\":true,\"pushRemind\":true,\"pushText\":true}]";
        final List<ConfigCmd> configCmds = JSONObject.parseArray(list, ConfigCmd.class);
        final SingleResponse<Boolean> booleanSingleResponse = messageController.saveConfig(configCmds);
        final String s = JsonUtil.toJson(booleanSingleResponse);
    }


    @Test
    @Order(1)
    @DisplayName("发送商品新建消息")
    @SneakyThrows
    public void itemAdd() {
        this.userInfo = new UserContext.UserInfo();
        userInfo.setUserId(10001L);
        userInfo.setUserName("单元测试01号");
        BackItemMessageEvent event = BackItemMessageEvent.buildEditBackItemEvent(1L, "消息测试商品01",
                "mock001", Optional.ofNullable(userInfo));
        EventBusUtil.post(event,true);
        System.in.read();
    }

    @Test
    @DisplayName("消息配置列表")
    public void messageConfig(){
        final SingleResponse<ConfigVoList> mapSingleResponse = messageController.listMessageConfig();
        System.out.println(JsonUtil.toJson(mapSingleResponse));
    }

    @Test
    @DisplayName("某一条消息设置为已读")
    public void read(){
        final SingleResponse<Boolean> read = messageController.read(13L);
    }

    @Test
    @DisplayName("一键读取")
    public void readAll(){
        final SingleResponse<Boolean> read = messageController.allRead(1L);
    }

    @Test
    @DisplayName("一键清除")
    public void removeAll(){
        final SingleResponse<Boolean> read = messageController.removeAll(1L);
    }


    @Test
    @DisplayName("未读消息数量")
    public void countNoRead(){
        final SingleResponse<Integer> integerSingleResponse = messageController.countNoRead(5847802L);
        System.out.println(JsonUtil.toJson(integerSingleResponse));
    }

    @Test
    @Order(2)
    @DisplayName("查询消息列表")
    public void testMessagePage() {
        ListPageQuery query = new ListPageQuery();
        query.setRecipientId(5847802L);
        query.setPushType(MessagePushType.ALL);
        query.setPageIndex(0);
        query.setPageSize(10);
        final PageResponse<MessageVO> pageVOPageResponse = messageController.pageMessages(query);
        System.out.println(JsonUtil.toJson(pageVOPageResponse));

//        "1".equals()
    }

    @Test
    @DisplayName("发送平台预警消息")
    @SneakyThrows
    public void testPlatform(){
        PlatformWarnMessageEvent event = PlatformWarnMessageEvent.build("123", "234", "sku对不上");
        EventBusUtil.post(event,true);
        System.in.read();

    }
    @Autowired
    IMessageService iMessageService;

    @Test
    public void test(){
        String json = "[{\"id\":null,\"configId\":1,\"template\":\"【%s】新增了商品【%s】【%s】,点击查看详情\",\"fillContent\":\"[{\\\"index\\\":0,\\\"val\\\":\\\"单元测试01号\\\",\\\"link\\\":\\\"\\\"},{\\\"index\\\":1,\\\"val\\\":\\\"mock001\\\",\\\"link\\\":\\\"http://p.dlab.cn/erp/commodity-management/back-end-good/review?id=11\\\"},{\\\"index\\\":2,\\\"val\\\":\\\"消息测试商品01\\\",\\\"link\\\":\\\"http://p.dlab.cn/erp/commodity-management/back-end-good/review?id=11\\\"}]\",\"deletedAt\":null,\"createdAt\":null,\"createdId\":null,\"updatedAt\":null,\"updatedId\":null,\"isDel\":null,\"state\":\"NO_READ\",\"type\":\"SYSTEM\",\"recipientId\":1},{\"id\":null,\"configId\":1,\"template\":\"【%s】新增了商品【%s】【%s】,点击查看详情\",\"fillContent\":\"[{\\\"index\\\":0,\\\"val\\\":\\\"单元测试01号\\\",\\\"link\\\":\\\"\\\"},{\\\"index\\\":1,\\\"val\\\":\\\"mock001\\\",\\\"link\\\":\\\"http://p.dlab.cn/erp/commodity-management/back-end-good/review?id=11\\\"},{\\\"index\\\":2,\\\"val\\\":\\\"消息测试商品01\\\",\\\"link\\\":\\\"http://p.dlab.cn/erp/commodity-management/back-end-good/review?id=11\\\"}]\",\"deletedAt\":null,\"createdAt\":null,\"createdId\":null,\"updatedAt\":null,\"updatedId\":null,\"isDel\":null,\"state\":\"NO_READ\",\"type\":\"SYSTEM\",\"recipientId\":2}]";
        List<Message> list = JsonUtil.parse(json, new TypeReference<List<Message>>() {
        });
        iMessageService.saveBatch(list);
    }

    @Autowired
    IMessageConfigService messageConfigService;


    @Test
    @DisplayName("消息配置自初始化")
    public void initConfig(){
        MsgFillObj fillObj1 = new MsgFillObj();
        fillObj1.setIndex(0);
        fillObj1.setVal("采购姓名");
        fillObj1.setColor("#64B8F8");

        MsgFillObj fillObj2 = new MsgFillObj();
        fillObj2.setIndex(0);
        fillObj2.setVal("商品编号");
        fillObj2.setColor("#64B8F8");

        MsgFillObj fillObj3 = new MsgFillObj();
        fillObj3.setIndex(0);
        fillObj3.setVal("商品名称");
        fillObj3.setColor("#64B8F8");

        final String s = JsonUtil.objToStr(Arrays.asList(fillObj1, fillObj2, fillObj3));

        LambdaUpdateWrapper<MessageConfig> wrapper = new LambdaUpdateWrapper<>();
        wrapper.set(MessageConfig::getSample,s).in(MessageConfig::getId,Arrays.asList(1L,2L));
        messageConfigService.update(wrapper);


    }

    @org.junit.Test
    public void sysPurchaseTest() throws Exception{
        String json = "{\n" +
                "        \"canEffect\": true,\n" +
                "        \"id\": 4,\n" +
                "        \"operationType\": \"SYS_PURCHASE_ORDER\",\n" +
                "        \"recipients\": [\n" +
                "            {\n" +
                "                \"value\": 7563904,\n" +
                "                \"label\": \"傅圣维\",\n" +
                "                \"key\": 7563904\n" +
                "            }\n" +
                "        ],\n" +
                "        \"pushDefault\": true,\n" +
                "        \"pushMail\": false,\n" +
                "        \"pushRemind\": true,\n" +
                "        \"pushText\": true\n" +
                "    }";
//        ConfigCmd configCmd = JSONObject.parseObject(json,ConfigCmd.class);
//        messageBizService.saveConfig(configCmd);

        SysPurchaseOrderEvent event = SysPurchaseOrderEvent.build(3);
        EventBusUtil.post(event,true);
        System.in.read();
    }



}
