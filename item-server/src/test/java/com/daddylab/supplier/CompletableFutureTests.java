package com.daddylab.supplier;

import org.junit.jupiter.api.Test;

import java.util.concurrent.CompletableFuture;

/**
 * <AUTHOR>
 * @since 2024/6/20
 */
public class CompletableFutureTests {

    @Test
    public void testExceptionForWhenCompleteThenRun() {
        CompletableFuture.supplyAsync(() -> {
            throw new RuntimeException("6");
        }).whenComplete((r, e) -> {
            if (e != null) {
                System.out.println("error");

            } else {
                System.out.println("result:" + r);
            }
        }).exceptionally(e -> {
            System.out.println("error2");
            return null;
        });
    }
}
