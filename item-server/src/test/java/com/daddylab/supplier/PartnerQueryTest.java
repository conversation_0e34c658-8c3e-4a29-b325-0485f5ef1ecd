package com.daddylab.supplier;

import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.containsStringIgnoringCase;
import static org.hamcrest.Matchers.not;

import com.daddylab.supplier.item.infrastructure.gatewayimpl.feign.PartnerFeignClient;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.feign.PartnerFeignClientFallbackFactory;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.feign.PartnerServerConfiguration;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.feign.dto.CommonCheckInfo;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.item.dto.PartnerItemReq;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.item.dto.PartnerItemResp;
import com.daddylab.supplier.item.infrastructure.goclient.Rsp;
import com.daddylab.supplier.item.infrastructure.utils.JsonUtil;
import java.util.List;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.cloud.openfeign.FeignAutoConfiguration;

/**
 * <AUTHOR>
 * @since 2022/6/3
 */
@EnableFeignClients(clients = PartnerFeignClient.class)
@SpringBootTest(classes = {
        PartnerFeignClientFallbackFactory.class,
        PartnerServerConfiguration.class,
        PartnerFeignClient.class,
        FeignAutoConfiguration.class,
}, properties = {
        "spring.cloud.nacos.config.enabled=false",
        "feign.hystrix.enabled=true",
        "spring.profiles.active=test",
        "partner-server.url=https://api.dlab.cn",
        "partner-server.api-token=RxOENnJX#eo4n$Uju1"
})
public class PartnerQueryTest {

    @Autowired
    PartnerFeignClient partnerFeignClient;

    @Test
    public void itemQuery() {
        final PartnerItemReq req = new PartnerItemReq();
        req.setSearchType(1);
        req.setContext("S013033");
        req.setPageIndex(1);
        req.setPageSize(10);

        final Rsp<List<PartnerItemResp>> listRsp = partnerFeignClient.itemQuery(req);
        System.out.println(listRsp);
    }

    @Test
    public void commonCheckInfo() {
        Assertions.assertDoesNotThrow(() -> {
            final Rsp<CommonCheckInfo> commonCheckInfoRsp = partnerFeignClient.commonCheckInfo(
                    6699L);

            final String json = JsonUtil.toJson(commonCheckInfoRsp);
            System.out.println(json);
            assertThat(json, not(containsStringIgnoringCase("\"null\"")));
        });
    }


}
