package com.daddylab.supplier;

import com.daddylab.supplier.item.infrastructure.validators.link.HttpURL;
import com.daddylab.supplier.item.infrastructure.validators.link.ValidateHelper;
import com.google.common.collect.Lists;

import io.swagger.annotations.ApiModelProperty;

import lombok.Data;

import org.hibernate.validator.group.GroupSequenceProvider;
import org.hibernate.validator.spi.group.DefaultGroupSequenceProvider;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import java.util.Arrays;
import java.util.List;
import java.util.Locale;
import java.util.Set;

import javax.validation.ConstraintViolation;
import javax.validation.Validation;
import javax.validation.Validator;
import javax.validation.ValidatorFactory;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Positive;
import javax.validation.constraints.PositiveOrZero;

/**
 * <AUTHOR>
 * @since 2022/7/10
 */
public class ValidatorTest {

    @Test
    @DisplayName("测试分组顺序提供器")
    public void groupSequenceProviderTest() {
        Locale.setDefault(Locale.CHINA);

        final Params params = new Params();
        //        params.setParam1(1L);
        //        params.setParam2(-1L);
        params.setLinks(Arrays.asList("ftp://123.com"));

        try (final ValidatorFactory validatorFactory = Validation.buildDefaultValidatorFactory()) {
            final Validator validator = validatorFactory.getValidator();
            final Set<ConstraintViolation<Params>> constraintViolations =
                    validator.validate(params);
            System.out.println(ValidateHelper.constraintsToOneLine(constraintViolations));
        }
    }

    interface Group2 {}

    public static class ParamsGroupSequenceProvider
            implements DefaultGroupSequenceProvider<Params> {

        @Override
        public List<Class<?>> getValidationGroups(Params object) {
            if (object != null && object.param1 != null) {
                return Lists.newArrayList(Group2.class, Params.class);
            }
            return Lists.newArrayList(Params.class);
        }
    }

    @Data
    @GroupSequenceProvider(ParamsGroupSequenceProvider.class)
    public static class Params {

        @Positive Long param1;

        @NotNull(groups = Group2.class)
        @PositiveOrZero
        Long param2;

        @ApiModelProperty("链接")
        List<@HttpURL(message = "链接需要是一个合法的HTTP URL") String> links;
    }
}
