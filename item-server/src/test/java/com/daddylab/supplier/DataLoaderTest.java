package com.daddylab.supplier;

import java.time.Duration;
import java.util.concurrent.CompletableFuture;
import lombok.extern.slf4j.Slf4j;
import org.dataloader.DataLoader;
import org.dataloader.DataLoaderFactory;
import org.dataloader.DataLoaderOptions;
import org.dataloader.registries.DispatchPredicate;
import org.dataloader.registries.ScheduledDataLoaderRegistry;
import org.junit.jupiter.api.Test;

/**
 * <AUTHOR>
 * @since 2022/7/5
 */
@Slf4j
public class DataLoaderTest {
    @Test
    public void testScheduledLoader() {
        final DataLoader<Integer, Integer> dataLoader = DataLoaderFactory
                .newDataLoader(keys -> {
                    log.info("loading {}", keys);
                    return CompletableFuture.supplyAsync(() -> keys);
                }, DataLoaderOptions.newOptions().setMaxBatchSize(10));
        final ScheduledDataLoaderRegistry scheduledDataLoaderRegistry = ScheduledDataLoaderRegistry
                .newScheduledRegistry()
                .dispatchPredicate(DispatchPredicate.dispatchIfDepthGreaterThan(10)
                        .or(DispatchPredicate.dispatchIfLongerThan(
                                Duration.ofSeconds(1))))
                .register("dataLoader", dataLoader)
                .build();

        final int count = 100;
        final CompletableFuture<?>[] futures = new CompletableFuture<?>[count];
        for (int i = 0; i < count; i++) {
            log.info("load {}", i);
            final CompletableFuture<Integer> future = dataLoader.load(i);
            futures[i] = future;
        }
        scheduledDataLoaderRegistry.dispatchAll();
        CompletableFuture.allOf(futures).join();
    }


}
