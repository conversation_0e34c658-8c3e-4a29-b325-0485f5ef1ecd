package com.daddylab.supplier;

import com.alibaba.cola.dto.SingleResponse;
import com.daddylab.supplier.item.application.salesInStock.SalesInStockBizService;
import com.daddylab.supplier.item.application.salesOutStock.SalesOutStockBizService;
import com.daddylab.supplier.item.application.salesOutStock.dto.SalesOutStockDetailVO;
import com.daddylab.supplier.item.infrastructure.utils.JsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;

/**
 * <AUTHOR> up
 * @date 2023年10月23日 4:05 PM
 */
@SpringBootTest(classes = {com.daddylab.supplier.item.ItemApplication.class},
        properties = {
                "spring.profiles.active:test"})
//@ExtendWith(SpringExtension.class)
//@SpringBootTest(classes = ItemApplication.class)
//@ActiveProfiles("test")
@Slf4j
public class StockInAndOutBizTest {

    @Resource
    SalesOutStockBizService salesOutStockBizService;

    @Resource
    SalesInStockBizService salesInStockBizService;

    @Test
    public void test(){
//        SalesOutStockPageQuery query = new SalesOutStockPageQuery();
//        PageResponse<SalesOutStockPageVO> response = salesOutStockBizService.queryPage(query);
//        System.out.println(JsonUtil.toJson(response));

        SingleResponse<SalesOutStockDetailVO> detailRes = salesOutStockBizService.viewDetail(483728L);
        System.out.println(JsonUtil.toJson(detailRes));
    }


}
