package com.daddylab.supplier;

import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.daddylab.supplier.item.application.handingsheet.HandingSheetTask;
import com.daddylab.supplier.item.application.nuonuo.NuoNuoBizService;
import com.daddylab.supplier.item.domain.messageRobot.enums.MessageRobotCode;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.INuoNuoInvoiceProcessRecordService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.INuoNuoInvoiceRequestService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.nuonuo.NuoNuoOpenGatewayImpl;
import com.daddylab.supplier.item.infrastructure.utils.Alert;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;

/**
 * <AUTHOR> up
 * @date 2024年07月26日 4:45 PM
 */
@SpringBootTest(classes = {com.daddylab.supplier.item.ItemApplication.class},
        properties = {
                "spring.profiles.active:test"})
//@ExtendWith(SpringExtension.class)
//@SpringBootTest(classes = ItemApplication.class)
//@ActiveProfiles("test")
@Slf4j
public class NuoNuoOpenTest {

    @Resource
    NuoNuoOpenGatewayImpl nuoNuoOpenGateway;

    @Resource
    INuoNuoInvoiceRequestService iNuoNuoInvoiceRequestService;

    @Resource
    INuoNuoInvoiceProcessRecordService iNuoNuoInvoiceProcessRecordService;


    @Resource
    NuoNuoBizService nuoNuoBizService;

    @Test
    public void testBillNew(){
//        NuoNuoInvoiceRequest request = new NuoNuoInvoiceRequest();
//        request.setOrderNo("6932314908065798009");
//        request.setInvoiceType(InvoiceType.pc);
//        request.setInvoiceTitleType(InvoiceTitleType.PERSON_OR_UNIT);
//        request.setInvoiceTitle("杭州老爸电商科技有限公司");
//        request.setTaxCode("***************");
//        request.setBank("招商银行");
//        request.setBankNo("654324324234231123232");
//        request.setCompanyAddress("杭州上城区");
//        request.setCompanyPhone("*********");
//        request.setMailAddress("<EMAIL>");
//        iNuoNuoInvoiceRequestService.save(request);
//
//        final NuoNuoInvoiceProcessRecord record = nuoNuoOpenGateway.requestBillingNew(request);

//        nuoNuoBizService.requestInvoiceNew(ListUtil.of("6932314908065798009"));

//        InvoiceNewCmd cmd = new InvoiceNewCmd();
//        cmd.setOrderNoList(ListUtil.of("3926887992477715826"));
//        cmd.setInvoiceType(InvoiceType.pc);
//        cmd.setInvoiceTitleType(InvoiceTitleType.PERSON_OR_UNIT);
//        cmd.setInvoiceTitle("杭州老爸电商科技有限公司");
//        cmd.setTaxCode("***************");
//        cmd.setBank("招商银行");
//        cmd.setBankNo("654324324234231123232");
//        cmd.setCompanyAddress("杭州上城区");
//        cmd.setCompanyPhone("*********");
//        cmd.setMailAddress("<EMAIL>");
//
//        nuoNuoBizService.requestInvoiceNew(cmd);

//        final SingleResponse<BigDecimal> bigDecimalSingleResponse = nuoNuoBizService.calculateInvoicedAmount(ListUtil.of("3943851012993772824"));
//        System.out.println(bigDecimalSingleResponse.getData());

        Alert.text(MessageRobotCode.INVOICE_NOTIFY, StrUtil.format("[诺诺开票异常] 订单号:{},错误:{}", "XXX", "XXX"));

    }

    @Test
    public void requestCreditNoteApplyNew(){

//        nuoNuoOpenGateway.queryInvoiceResult(ListUtil.of("24073110020403101967"),"ORD003");

        nuoNuoBizService.creditNoteApply(1722441600L, 1723046400L);

    }

    @Test
    public void test(){
        SpringUtil.getBean(HandingSheetTask.class).toBeConfirmNoticeSummary();
    }

}
