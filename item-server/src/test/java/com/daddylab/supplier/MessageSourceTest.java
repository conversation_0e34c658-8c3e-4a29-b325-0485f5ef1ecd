package com.daddylab.supplier;

import com.daddylab.supplier.item.infrastructure.utils.MessageSourcePlus;
import java.util.Arrays;
import java.util.Locale;
import java.util.Map;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.context.MessageSourceAutoConfiguration;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.MessageSource;

/**
 * <AUTHOR>
 * @since 2022/6/3
 */
@SpringBootTest(properties = {
        "spring.cloud.nacos.config.enabled=false",
//        "logging.level.org.springframework.boot.autoconfigure=debug",
//        "spring.messages.basename=messages"
}, classes = {
        MessageSourceAutoConfiguration.class,
        MessageSourcePlus.class
})
public class MessageSourceTest {
    @Autowired
    MessageSource messageSource;

    @Autowired
    MessageSourcePlus messageSourcePlus;

    @Test
    public void messageSource() {
        final String itemId = messageSource.getMessage("itemId", null, "itemId", Locale.getDefault());
        System.out.println(itemId);
    }

    @Test
    public void messageSourcePlus() {
        final Map<String, String> messages = messageSourcePlus
                .getMessages(Arrays.asList("itemId", "qcIds"), "log", "newGoods");
        System.out.println(messages);
    }
}
